# 规则引擎系统说明文档

## 📋 目录

- [1. 项目概述](#1-项目概述)
- [2. 系统架构](#2-系统架构)
- [3. 功能模块](#3-功能模块)
- [4. 技术实现](#4-技术实现)
- [5. 使用指南](#5-使用指南)
- [6. 开发说明](#6-开发说明)
- [7. 部署配置](#7-部署配置)

## 1. 项目概述

### 1.1 系统简介

规则引擎系统是一个基于 Vue 2 + Element UI + LogicFlow 的可视化规则链管理平台。系统提供了完整的规则链生命周期管理，包括创建、编辑、配置、调试、版本控制等功能。

### 1.2 核心特性

- **可视化编辑**：基于 LogicFlow 的拖拽式流程图编辑器
- **完整的 CRUD 操作**：使用 BuseCrud 组件实现标准化的增删改查
- **多种节点类型**：支持条件判断、循环、分支、并行等多种业务节点
- **调试功能**：支持节点级别的调试和测试
- **版本控制**：完整的版本管理和回滚功能
- **权限控制**：集成项目权限系统
- **响应式设计**：适配不同屏幕尺寸

### 1.3 技术栈

- **前端框架**：Vue 2.6.14
- **UI 组件库**：Element UI
- **流程图引擎**：@logicflow/core
- **业务组件**：@bangdao/buse-components-element
- **样式预处理**：Less
- **构建工具**：Webpack

## 2. 系统架构

### 2.1 目录结构

```
src/views/ruleEngine/
├── index.vue                    # 规则链列表页面
├── details.vue                  # 规则链配置页面
├── components/
│   ├── RuleChainModal.vue       # 规则链新增/编辑弹窗
│   └── NodeFlow/                # 流程图编辑器组件
│       ├── index.vue            # 主组件
│       ├── config/              # 配置文件
│       │   ├── logicFlowConfig.js    # LogicFlow 配置
│       │   ├── nodeTypes.js          # 节点类型定义
│       │   ├── icons.js              # 图标配置
│       │   ├── CustomNode.js         # 自定义节点
│       │   ├── StartNode.js          # 开始节点
│       │   ├── FunctionNode.js       # 功能节点
│       │   └── SubChainNode.js       # 子流程节点
│       ├── utils/               # 工具函数
│       │   ├── defaultFormData.js    # 默认表单数据
│       │   └── handleNode.js         # 节点处理工具
│       └── components/          # 子组件
│           ├── NodePanel.vue         # 节点面板
│           ├── FlowToolbar.vue       # 工具栏
│           ├── NodeDrawer.vue        # 节点配置抽屉
│           ├── EdgeModal.vue         # 连线配置弹窗
│           ├── EntityPanel.vue       # 调试面板
│           ├── ExpressionPreview.vue # 表达式预览
│           ├── VersionModal.vue      # 版本控制弹窗
│           └── TestModal.vue         # 测试弹窗
```

### 2.2 组件关系图

```mermaid
graph TD
    A[规则链列表页面] --> B[规则链配置页面]
    B --> C[NodeFlow 主组件]
    C --> D[节点面板]
    C --> E[工具栏]
    C --> F[节点配置抽屉]
    C --> G[连线配置弹窗]
    C --> H[表达式预览]
    C --> I[版本控制弹窗]
    C --> J[测试弹窗]
    A --> K[规则链弹窗]
```

## 3. 功能模块

### 3.1 规则链列表管理

**页面路径**：`/rule-engine/index`

**主要功能**：
- 规则链列表展示（分页、筛选、排序）
- 新增规则链
- 编辑规则链基本信息
- 删除规则链
- 复制规则链
- 跳转到配置页面
- 导出功能

**权限控制**：
- `rule:chain:add` - 新增权限
- `rule:chain:edit` - 编辑权限
- `rule:chain:remove` - 删除权限
- `rule:chain:config` - 配置权限
- `rule:chain:export` - 导出权限

### 3.2 规则链配置管理

**页面路径**：`/rule-engine/details`

**主要功能**：
- 可视化流程图编辑
- 节点拖拽创建
- 节点属性配置
- 连线配置
- 调试模式
- 版本控制
- 表达式预览
- 保存配置

### 3.3 节点类型支持

#### 3.3.1 基础节点
- **开始节点**：流程起点，每个规则链必须有且仅有一个
- **普通节点**：基础业务处理节点

#### 3.3.2 控制节点
- **条件节点 (IF)**：支持 true/false 分支
- **分支节点 (SWITCH)**：支持多条件分支
- **循环节点**：
  - FOR：次数循环
  - WHILE：条件循环
  - ITERATOR：迭代循环

#### 3.3.3 功能节点
- **并行节点 (PAR)**：并行处理
- **超时节点 (TIMEOUT)**：超时处理
- **结束节点 (END)**：流程结束
- **子流程节点 (SUBFLOW)**：调用子规则链

## 4. 技术实现

### 4.1 核心技术选型

#### 4.1.1 BuseCrud 组件
- **作用**：提供标准化的列表页面功能
- **特性**：集成筛选、分页、操作按钮、弹窗表单
- **配置**：通过 computed 属性配置表格列、筛选项、分页等

#### 4.1.2 LogicFlow 流程图引擎
- **作用**：提供可视化流程图编辑能力
- **特性**：节点拖拽、连线、自定义节点、事件监听
- **扩展**：自定义节点样式、菜单、工具栏

#### 4.1.3 Element UI 适配
- **原始组件**：Ant Design Vue (a-button, a-dropdown 等)
- **适配组件**：Element UI (el-button, el-dropdown 等)
- **适配内容**：组件 API、样式、事件处理

### 4.2 关键实现细节

#### 4.2.1 节点自定义
```javascript
// 自定义节点示例
class CustomNodeModel extends RectNodeModel {
  initNodeData(data) {
    super.initNodeData(data);
    this.width = 150;
    this.height = 40;
    // 设置锚点
    this.anchorsOffset = [
      [-this.width / 2, 0], // 左侧锚点
      [this.width / 2, 0],  // 右侧锚点
    ];
  }
}
```

#### 4.2.2 事件处理
```javascript
// LogicFlow 事件监听
lf.on('node:add', ({ data }) => {
  // 节点添加处理
});

lf.on('edge:add', ({ data }) => {
  // 连线添加处理
});
```

#### 4.2.3 数据流转
```javascript
// 保存流程数据
const flowData = lf.getGraphData();
const processedData = {
  nodes: flowData.nodes.map(node => ({
    ...node,
    properties: {
      ...node.properties,
      name: node.text?.value || node.properties?.name || '',
    },
  })),
  edges: flowData.edges.map(edge => ({
    ...edge,
    properties: {
      ...edge.properties,
      edgeType: edge.text?.value || edge.properties?.edgeType || '',
    },
  })),
  expression: this.expression,
};
```

## 5. 使用指南

### 5.1 基本操作流程

1. **访问系统**
   - 打开浏览器访问：`http://localhost:8080/charging-maintenance-ui/rule-engine/index`

2. **创建规则链**
   - 点击"新增规则链"按钮
   - 填写规则链名称、描述等信息
   - 点击确定保存

3. **配置规则链**
   - 在列表中点击"配置"按钮
   - 进入可视化编辑器
   - 从左侧面板拖拽节点到画布
   - 配置节点属性和连线
   - 保存配置

4. **调试测试**
   - 点击工具栏"调试"按钮进入调试模式
   - 右键节点选择"自测"
   - 输入测试数据查看执行结果

### 5.2 节点配置说明

#### 5.2.1 条件节点 (IF)
- **用途**：根据条件进行分支判断
- **配置**：设置判断条件表达式
- **连线**：必须有 true 和 false 两个分支

#### 5.2.2 分支节点 (SWITCH)
- **用途**：多条件分支判断
- **配置**：设置多个条件值
- **连线**：每个条件对应一个分支，包含默认分支

#### 5.2.3 循环节点
- **FOR**：设置循环次数
- **WHILE**：设置循环条件
- **ITERATOR**：设置迭代对象

### 5.3 快捷键说明

- `Ctrl/Cmd + S`：保存流程图
- `Ctrl/Cmd + Z`：撤销操作
- `Ctrl/Cmd + Y`：重做操作
- `Ctrl/Cmd + A`：全选节点（除开始节点）
- `Delete/Backspace`：删除选中元素

## 6. 开发说明

### 6.1 开发环境搭建

1. **安装依赖**
   ```bash
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   ```

3. **访问系统**
   ```
   http://localhost:8080/charging-maintenance-ui/rule-engine/index
   ```

### 6.2 新增节点类型

1. **定义节点类型**
   ```javascript
   // 在 nodeTypes.js 中添加
   {
     type: 'custom-node',
     label: '新节点',
     nodeType: 'NEW_TYPE',
     properties: {
       icon: 'new-icon',
       color: '#1890ff',
     },
   }
   ```

2. **创建节点类**
   ```javascript
   // 创建 NewNode.js
   class NewNodeModel extends RectNodeModel {
     // 节点逻辑
   }
   
   class NewNodeView extends RectNode {
     // 节点视图
   }
   ```

3. **注册节点**
   ```javascript
   // 在 logicFlowConfig.js 中注册
   lf.register(NewNode);
   ```

### 6.3 API 接口集成

系统预留了 API 接口调用位置，需要根据实际后端接口进行集成：

```javascript
// 列表页面 API
async loadData() {
  const response = await this.$post('/api/rule-chain/list', this.params);
  this.tableData = response.data.list;
  this.total = response.data.total;
}

// 详情页面 API
async loadChainData() {
  const response = await this.$get(`/api/rule-chain/${this.chainId}`);
  this.flowData = response.data;
}

// 保存配置 API
async handleSave() {
  await this.$post('/api/rule-chain/save', this.flowData);
}
```

## 7. 部署配置

### 7.1 生产环境构建

```bash
npm run build
```

### 7.2 路由配置

系统路由已配置在 `src/router/index.js` 中：

```javascript
{
  path: "/rule-engine",
  component: Layout,
  children: [
    {
      path: "index",
      component: () => import("@/views/ruleEngine/index.vue"),
      name: "规则引擎列表",
    },
    {
      path: "details",
      component: () => import("@/views/ruleEngine/details.vue"),
      name: "规则链配置",
    },
  ],
}
```

### 7.3 权限配置

需要在权限系统中配置以下权限：

- `rule:chain:add` - 新增规则链
- `rule:chain:edit` - 编辑规则链
- `rule:chain:remove` - 删除规则链
- `rule:chain:config` - 配置规则链
- `rule:chain:export` - 导出规则链

---

## 📞 技术支持

如有问题或建议，请联系开发团队。

**文档版本**：v1.0  
**更新时间**：2024-01-20  
**维护人员**：开发团队
