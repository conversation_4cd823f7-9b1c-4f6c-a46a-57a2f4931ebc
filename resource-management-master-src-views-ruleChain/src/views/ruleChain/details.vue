<template>
  <div>
    <a-spin :spinning="loading">
      <sm-node-flow
        :chainId="chainId"
        :isDebug="isDebug"
        :defaultData="flowData"
        @save="saveFlowData"
        @restore="handleRestore"
        @jumpToSubChain="handleJumpToSubChain"
      />
    </a-spin>

    <!-- 新增的时候才会展示 -->
    <rule-chain-modal :modalVisible="modalVisible" :formData="formData" :isEdit="false" @cancel="handleCancel" />
  </div>
</template>
<script>
import smNodeFlow from './components/NodeFlow/index.vue';
import RuleChainModal from './components/RuleChainModal.vue';

const defaultFlowData = {
  chainId: '',
  chainName: '',
  chainDesc: '',
  expression: '',
  nodes: [],
  edges: [],
};

export default {
  components: {
    smNodeFlow,
    RuleChainModal,
  },
  data() {
    return {
      flowData: { ...defaultFlowData },
      chainId: '',
      modalVisible: false,
      formData: {
        chainName: '',
        chainDesc: '',
      },
      // 临时节点数据
      tempFlowData: {
        nodes: [],
        edges: [],
        expression: '',
      },
      isDebug: false,
      loading: false,
    };
  },

  async activated() {
    // 从localStorage读取调试状态
    this.isDebug = localStorage.getItem('isDebug') === 'true';

    const query = this.$route.query;
    if (query.chainId) {
      // 编辑模式
      this.chainId = query.chainId;
      await this.getRuleChainDetails(this.chainId);
    } else {
      // 新增模式
      this.flowData = { ...defaultFlowData };
    }
  },

  methods: {
    async getRuleChainDetails(chainId) {
      if (!chainId) return;
      this.loading = true;
      const [res, err] = await this.$get(this.api.rule_chain_detail, {
        id: chainId,
      });
      this.loading = false;
      if (err) {
        return;
      }
      if (res?.success) {
        this.flowData = res.data;
        this.formData = {
          chainName: res.data?.chainName,
          chainDesc: res.data?.chainDesc,
        };
      }
    },

    // 保存流程数据 data 只有 nodes edges expression
    async saveFlowData({ nodes, edges, expression }) {
      this.tempFlowData = { nodes, edges, expression };
      if (this.chainId) {
        this.$confirm({
          title: '提示',
          content: '是否要生成历史版本?',
          okText: '是',
          cancelText: '否',
          onOk: async () => {
            this.loading = true;
            const [res, err] = await this.$post(this.api.rule_chain_update, {
              chainId: this.chainId,
              chainName: this.formData.chainName,
              chainDesc: this.formData.chainDesc,
              nodes,
              edges,
              expression,
              isHistory: true,
            });
            this.loading = false;
            if (err) {
              return;
            }
            // 更新版本号并关闭调试模式
            if (res.data?.version) {
              this.flowData = {
                ...this.flowData,
                version: res.data.version,
              };
            }
            // 关闭调试模式
            localStorage.setItem('isDebug', 'false');
            this.$message.success('保存成功,并生成历史版本');
          },
          onCancel: async () => {
            this.loading = true;
            const [res, err] = await this.$post(this.api.rule_chain_update, {
              chainId: this.chainId,
              chainName: this.formData.chainName,
              chainDesc: this.formData.chainDesc,
              nodes,
              edges,
              expression,
              isHistory: false,
            });
            this.loading = false;
            if (err) {
              return;
            }
            // 更新版本号并关闭调试模式
            if (res.data?.version) {
              this.flowData = {
                ...this.flowData,
                version: res.data.version,
              };
            }
            // 关闭调试模式
            localStorage.setItem('isDebug', 'false');
            this.$message.success('保存成功');
          },
        });
      }
      // 新增模式
      if (!this.formData.chainName?.trim()) {
        this.modalVisible = true;
        return;
      }
    },
    handleRestore(record) {
      // 刷新当前页面
      window.location.reload();
    },

    // 处理弹框取消
    async handleCancel(isSubmit = false) {
      this.modalVisible = false;
      if (isSubmit) {
        // 提交新增
        const { nodes, edges, expression } = this.tempFlowData;
        this.loading = true;
        const [res, err] = await this.$post(this.api.rule_chain_add, {
          chainName: this.formData.chainName,
          chainDesc: this.formData.chainDesc,
          nodes,
          edges,
          expression,
        });
        this.loading = false;
        if (err) {
          return;
        }
        this.$message.success('保存成功');
        this.$router.back();
      }
    },
    handleJumpToSubChain(subChainId) {
      console.log('subChainId', subChainId);
      if (!subChainId) {
        this.$message.warning('子流程ID不能为空');
        return;
      }
      if (subChainId === this.chainId) {
        this.$message.warning('不能跳转到当前规则链');
        return;
      }
      localStorage.setItem('isDebug', 'false');
      this.$router.push({
        name: 'VPP_RESOURCE-ruleChain-details',
        query: {
          chainId: subChainId,
        },
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
