<template>
  <a-modal
    :visible="modalVisible"
    :title="isEdit ? '编辑规则链' : '新增规则链'"
    @ok="handleSubmit"
    @cancel="handleCancel()"
    :maskClosable="false"
  >
    <a-form-model
      :model="formData"
      :rules="rules"
      ref="formRef"
      labelAlign="right"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-model-item label="规则链名称" prop="chainName">
        <a-input v-model="formData.chainName" placeholder="请输入规则链名称" :maxLength="32" :disabled="isEdit" />
      </a-form-model-item>
      <a-form-model-item label="规则链描述" prop="chainDesc">
        <a-input v-model="formData.chainDesc" placeholder="请输入规则链描述" :maxLength="64" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
export default {
  name: 'RuleChainModal',
  props: {
    modalVisible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({
        chainName: '',
        chainDesc: '',
      }),
    },
    isEdit: {
      type: Boolean,
      default: true, // 默认是编辑模式
    },
  },
  data() {
    return {
      rules: {
        chainName: [
          { required: true, message: '请输入规则链名称', trigger: 'blur' },
          { max: 32, message: '规则链名称最多32个字符', trigger: 'blur' },
        ],
        chainDesc: [{ max: 64, message: '规则链描述最多64个字符', trigger: 'blur' }],
      },
    };
  },

  methods: {
    async handleSubmit() {
      const valid = await this.$refs.formRef.validate();
      if (valid) {
        this.handleCancel(true);
      }
    },
    handleCancel(isSubmit = false) {
      this.$emit('cancel', isSubmit);
      this.resetForm();
    },
    resetForm() {
      this.$refs.formRef?.resetFields();
      this.$refs.formRef?.clearValidate();
    },
  },
};
</script>
