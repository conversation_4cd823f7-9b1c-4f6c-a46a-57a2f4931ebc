export const nodeTypes = {
  el: [
    {
      type: 'custom-node',
      label: '节点',
      nodeType: 'THEN',
      properties: {
        icon: 'then',
        color: '#1890ff',
      },
    },
    {
      type: 'custom-node',
      label: '选择',
      nodeType: 'IF',
      properties: {
        icon: 'if',
        color: '#722ed1',
      },
      edgeTags: ['true', 'false'],
    },
    {
      type: 'custom-node',
      label: '条件',
      nodeType: 'SWITCH',
      properties: {
        icon: 'switch',
        color: '#722ed1',
      },
      edgeTags: ['DEFAULT'],
    },
    {
      type: 'custom-node',
      label: '次数循环',
      nodeType: 'FOR',
      properties: {
        icon: 'for',
        color: '#fa8c16',
      },
      edgeTags: ['DO', 'BREAK'],
    },
    {
      type: 'custom-node',
      label: '条件循环',
      nodeType: 'WHILE',
      properties: {
        icon: 'while',
        color: '#fa8c16',
      },
      edgeTags: ['DO', 'BREAK'],
    },
    {
      type: 'custom-node',
      label: '迭代循环',
      nodeType: 'ITERATOR',
      properties: {
        icon: 'iterator',
        color: '#fa8c16',
      },
      edgeTags: ['DO', 'BREAK'],
    },
    // 子流程
    {
      type: 'sub-chain-node',
      label: '子流程',
      nodeType: 'SUBFLOW',
      properties: {
        icon: 'subFlow',
        color: '#eb2f96',
      },
    },
  ],
  // 功能节点
  function: [
    {
      type: 'function-node',
      label: '超时',
      nodeType: 'TIMEOUT',
      properties: {
        icon: 'timeout',
        color: '#f5222d',
      },
    },
    // 并行
    {
      type: 'function-node',
      label: '并行',
      nodeType: 'PAR',
      properties: {
        icon: 'parallel',
        color: '#13c2c2',
      },
    },
    // 结束节点
    {
      type: 'function-node',
      label: '结束',
      nodeType: 'END',
      properties: {
        icon: 'end',
        color: '#8c8c8c',
      },
    },
  ],
  // // 工具节点
  // tool: [
  //   {
  //     type: 'custom-node',
  //     label: '公式',
  //     nodeType: 'FORMULA',
  //     properties: {
  //       icon: 'formula',
  //       color: '#1890ff',
  //     },
  //   },
  // ],
};

export const typeHeaderMap = {
  el: 'EL表达式',
  function: '功能节点',
  // tool: '工具节点',
};

export const getElNodeConfigByType = (nodeType) => {
  return nodeTypes.el.find((item) => item.nodeType === nodeType);
};
