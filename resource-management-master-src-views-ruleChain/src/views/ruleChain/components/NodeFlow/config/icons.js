export const nodeIcons = {
  // 顺序节点 - 流畅优雅的箭头
  then: 'M121.6 405.333333c-59.733333 0-106.666667 46.933333-106.666667 106.666667s46.933333 106.666667 106.666667 106.666667 106.666667-46.933333 106.666667-106.666667-49.066667-106.666667-106.666667-106.666667zM902.4 405.333333c-59.733333 0-106.666667 46.933333-106.666667 106.666667s46.933333 106.666667 106.666667 106.666667 106.666667-46.933333 106.666667-106.666667-46.933333-106.666667-106.666667-106.666667zM219.733333 492.8h659.2v38.4H219.733333z',

  // 分支节点 - 简洁的菱形图标
  if: 'M512 128L128 512l384 384 384-384L512 128zM256 512l256-256 256 256-256 256L256 512z',

  // 多节点 - 多分支的图标
  switch:
    'M917.333333 896c-58.813333 0-106.666667-47.853333-106.666666-106.666667 0-51.513333 36.706667-94.613333 85.333333-104.526666V522.666667a10.666667 10.666667 0 0 0-10.666667-10.666667H554.666667v172.806667c48.626667 9.913333 85.333333 53.013333 85.333333 104.526666 0 58.813333-47.853333 106.666667-106.666667 106.666667s-106.666667-47.853333-106.666666-106.666667c0-51.513333 36.706667-94.613333 85.333333-104.526666V512H181.333333a10.666667 10.666667 0 0 0-10.666666 10.666667v162.14c48.626667 9.913333 85.333333 53.013333 85.333333 104.526666 0 58.813333-47.853333 106.666667-106.666667 106.666667s-106.666667-47.853333-106.666666-106.666667c0-51.513333 36.706667-94.613333 85.333333-104.526666V522.666667a53.393333 53.393333 0 0 1 53.333333-53.333334h330.666667V339.193333C463.373333 329.28 426.666667 286.18 426.666667 234.666667c0-58.813333 47.853333-106.666667 106.666666-106.666667s106.666667 47.853333 106.666667 106.666667c0 51.513333-36.706667 94.613333-85.333333 104.526666V469.333333h330.666666a53.393333 53.393333 0 0 1 53.333334 53.333334v162.14c48.626667 9.913333 85.333333 53.013333 85.333333 104.526666 0 58.813333-47.853333 106.666667-106.666667 106.666667z',

  // for循环 - 简洁的循环箭头
  for: 'M111.631027 242.582809l171.740042-145.979035c137.392034-128.805031 386.415094-128.805031 532.39413 0l-85.870021 68.696016c-94.457023-85.870021-266.197065-85.870021-369.24109 0L214.675052 302.691824H343.480084v103.044025H0V105.190776h111.631027v137.392033zM686.960168 697.69392V594.649895h343.480084v317.719078h-111.631028V766.389937l-163.15304 154.566038c-68.696017 68.696017-163.15304 103.044025-257.610062 103.044025-103.044025 0-188.914046-34.348008-257.610063-103.044025l77.283019-77.283019c94.457023 85.870021 266.197065 85.870021 360.654088 0l154.566037-137.392034H686.960168zM592.503145 543.127883c8.587002-8.587002 25.761006-8.587002 34.348008-17.174005 8.587002-8.587002 8.587002-17.174004 17.174004-25.761006 0-8.587002 8.587002-17.174004 0-25.761006 0-8.587002-8.587002-17.174004-8.587002-34.348009l-17.174004-17.174004c-8.587002-8.587002-17.174004-17.174004-25.761006-17.174004h-34.348009c-8.587002 0-25.761006 8.587002-42.93501 17.174004L352.067086 517.366876h-25.761006l-17.174005-17.174004c-8.587002-8.587002-8.587002-25.761006-8.587002-34.348008 0-8.587002 8.587002-17.174004 8.587002-17.174004l274.784068-163.15304h25.761006c8.587002 8.587002 17.174004 8.587002 25.761006 25.761006 8.587002 8.587002 8.587002 17.174004 8.587002 25.761006 0 8.587002-8.587002 17.174004-8.587002 17.174004l-34.348008 17.174005h25.761006c8.587002 0 17.174004 0 25.761006 8.587002 8.587002 0 17.174004 8.587002 25.761007 17.174004l25.761006 25.761006c8.587002 17.174004 17.174004 34.348008 17.174004 51.522013v51.522012c0 17.174004-8.587002 25.761006-25.761006 42.935011 0 8.587002-8.587002 17.174004-34.348009 34.348008L472.285115 714.867925c-8.587002 0-17.174004 8.587002-25.761006 0-8.587002 0-17.174004-8.587002-25.761006-17.174005 0-17.174004-8.587002-25.761006-8.587002-34.348008 0-8.587002 8.587002-17.174004 8.587002-17.174004l171.740042-103.044025z',

  // while循环 - 简洁的循环标志
  while:
    'M128.1 384.4c0-71 57.5-128.5 128.5-128.5h522.9l-85.2 85.2c-3.2 3.2-3.2 8.4 0 11.5l33.9 33.6c3.2 3.1 8.3 3.1 11.5 0l155.1-155.1c4-4 4-10.6 0-14.6L728.4 50.2c-4-4-10.6-4-14.6 0l-30.6 30.6c-4 4-4 10.6 0 14.6l96.5 96.5H256.6c-106.3 0-192.5 86.2-192.5 192.5v309.1c0 5.7 4.6 10.3 10.3 10.3h43.3c5.7 0 10.3-4.6 10.3-10.3V384.4zM896.2 330.3v309c0 71-57.5 128.5-128.5 128.5H245.3l96.5-96.5c4-4 4-10.6 0-14.6L311.2 626c-4-4-10.6-4-14.6 0L130.4 792.2c-4.1 4.1-4.1 10.8 0 15l166.3 166.3c4 4 10.6 4 14.6 0l30.6-30.6c4-4 4-10.6 0-14.6l-96.4-96.4h522.1c106.3 0 192.5-86.2 192.5-192.5v-309c0-5.7-4.6-10.3-10.3-10.3h-43.3c-5.7-0.1-10.3 4.5-10.3 10.2z M442 671.9L282.9 512.8c-4.2-4.2-4.2-11 0-15.2l30.1-30.1c4.2-4.2 11-4.2 15.2 0l121.2 121.2 210.4-213c3.2-3.2 8.3-3.2 11.5-0.1l34 33.5c3.2 3.2 3.2 8.3 0.1 11.5l-248 251.1c-4.4 4.4-11.2 4.4-15.4 0.2z',

  // iterator - 简洁的迭代器图标
  iterator:
    'M512 42.666667h-3.413333l-17.92-16.213334c-1.706667-1.706667-4.266667-0.853333-4.266667 1.706667v15.36C239.786667 56.32 42.666667 261.973333 42.666667 512c0 252.586667 200.533333 459.093333 451.413333 468.48l18.773333 17.066667c1.706667 1.706667 4.266667 0 4.266667-1.706667V981.333333c256.853333-2.56 465.066667-211.626667 465.066667-469.333333C981.333333 253.44 770.56 42.666667 512 42.666667z M516.266667 968.533333v-18.773333c0-1.706667-2.56-3.413333-4.266667-1.706667l-22.186667 20.48c-241.493333-11.093333-435.2-211.626667-435.2-455.68C55.466667 268.8 246.613333 69.973333 486.4 56.32v17.92c0 1.706667 2.56 3.413333 4.266667 1.706667l23.04-20.48c250.88 0.853333 455.68 204.8 455.68 456.533333-0.853333 250.026667-203.093333 453.973333-453.12 456.533333z M512 0C229.546667 0 0 229.546667 0 512s229.546667 512 512 512 512-229.546667 512-512S794.453333 0 512 0z M512 1006.933333C238.933333 1006.933333 17.066667 785.066667 17.066667 512S238.933333 17.066667 512 17.066667s494.933333 221.866667 494.933333 494.933333-221.866667 494.933333-494.933333 494.933333z',
  // 超时
  timeout:
    'M512 85.333333c-235.52 0-426.666667 191.146667-426.666667 426.666667s191.146667 426.666667 426.666667 426.666667 426.666667-191.146667 426.666667-426.666667-191.146667-426.666667-426.666667-426.666667z m0 810.666667c-211.968 0-384-172.032-384-384s172.032-384 384-384 384 172.032 384 384-172.032 384-384 384z M533.333333 298.666667h-42.666666v256l224 134.4 21.333333-35.2-202.666667-121.6z',
  //  并行
  parallel:
    'M320 192h64v128h-64v-128zm0 192h64v128h-64v-128zm0 192h64v128h-64v-128zm320-384h64v128h-64v-128zm0 192h64v128h-64v-128zm0 192h64v128h-64v-128zm-160-384h64v128h-64v-128zm0 192h64v128h-64v-128zm0 192h64v128h-64v-128zM192 448l128-128v256L192 448zm640 0l-128 128v-256l128 128z',
  // 结束
  end: 'M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zM384 384h256v256H384V384z',
  // 公式
  formula:
    'M810.666667 170.666667H213.333333c-23.466667 0-42.666667 19.2-42.666666 42.666666v597.333334c0 23.466667 19.2 42.666667 42.666666 42.666666h597.333334c23.466667 0 42.666667-19.2 42.666666-42.666666V213.333333c0-23.466667-19.2-42.666667-42.666666-42.666666z M341.333333 682.666667h-85.333333v-85.333334h85.333333v85.333334z m0-170.666667h-85.333333v-85.333333h85.333333v85.333333z m426.666667 170.666667H426.666667v-85.333334h341.333333v85.333334z m0-170.666667H426.666667v-85.333333h341.333333v85.333333z',
  // 子流程
  subFlow:
    'M896 298.666667v426.666666c0 46.933333-38.4 85.333333-85.333333 85.333334H213.333333c-46.933333 0-85.333333-38.4-85.333333-85.333334V298.666667c0-46.933333 38.4-85.333333 85.333333-85.333334h597.333334c46.933333 0 85.333333 38.4 85.333333 85.333334zM213.333333 256c-23.466667 0-42.666667 19.2-42.666666 42.666667v426.666666c0 23.466667 19.2 42.666667 42.666666 42.666667h597.333334c23.466667 0 42.666667-19.2 42.666666-42.666667V298.666667c0-23.466667-19.2-42.666667-42.666666-42.666667H213.333333z M384 384h256v256H384V384z',
};
