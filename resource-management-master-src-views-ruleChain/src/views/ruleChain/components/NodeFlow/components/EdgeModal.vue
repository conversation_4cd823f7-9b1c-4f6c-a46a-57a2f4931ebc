<template>
  <a-modal
    title="连线配置"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="loading"
    :width="600"
    :maskClosable="false"
    class="edge-modal"
  >
    <a-form-model ref="form" :model="formData" :rules="rules">
      <a-form-model-item label="连线类型" prop="edgeType">
        <!-- Switch节点使用可输入的选择框 -->
        <a-select
          v-if="edgeData?.sourceNode?.properties?.nodeType === 'SWITCH'"
          v-model="formData.edgeType"
          style="width: 100%"
          placeholder="请选择分支条件"
          mode="tags"
          :maxTagCount="1"
          :maxTagTextLength="20"
          @change="handleEdgeTypeChange"
        >
          <a-select-option v-for="tag in computedAvailableTags" :key="tag" :value="tag">
            {{ tag }}
          </a-select-option>
        </a-select>

        <!-- 其他节点使用普通选择框 -->
        <a-select v-else v-model="formData.edgeType" style="width: 100%" placeholder="请选择连线类型">
          <a-select-option v-for="(tag, index) in computedAvailableTags" :key="tag + index" :value="tag">
            {{ tag }}
          </a-select-option>
        </a-select>
      </a-form-model-item>

      <!-- 添加异常处理配置 -->
      <a-form-model-item label="异常处理" prop="catchStart">
        <a-radio-group v-model="formData.catchStart">
          <a-radio value="true">开始</a-radio>
          <a-radio value="false">结束</a-radio>
        </a-radio-group>
      </a-form-model-item>

      <!-- 添加跳出循环体配置 -->
      <a-form-model-item label="跳出循环体" prop="breakLoop">
        <a-switch v-model="formData.breakLoop" />
      </a-form-model-item>

      <a-form-model-item>
        <common-code-form
          :formRequired="false"
          :formData="formData"
          codeTips="连线代码需要返回一个布尔值(true/false)。例如: true 或 false"
        />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import CommonCodeForm from './nodeForms/CommonCodeForm.vue';
import { getElNodeConfigByType } from '../config/nodeTypes';
export default {
  name: 'EdgeDrawer',
  components: {
    CommonCodeForm,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    edgeData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      formData: {
        edgeType: undefined, // 连线类型
        scriptCode: '',
        scriptName: '',
        isBuiltIn: false,
        exceptionHandling: undefined, // 异常处理配置
        breakLoop: false, // 是否跳出循环体
      },
      rules: {
        edgeType: [
          { required: true, message: '请选择连线类型' },
          {
            validator: (rule, value, callback) => {
              if (Array.isArray(value)) {
                // 对于 Switch 节点的 tags 模式
                if (!value.length || !value[0] || value[0].trim() === '') {
                  callback(new Error('请选择或输入连线类型'));
                }
              } else if (!value || value.trim() === '') {
                // 对于普通选择模式
                callback(new Error('请选择连线类型'));
              }
              callback();
            },
          },
        ],
        exceptionHandling: [{ required: true, message: '请选择异常处理时机' }],
      },
    };
  },
  computed: {
    computedAvailableTags() {
      const nodeType = this.edgeData?.sourceNode?.properties?.nodeType;
      const elConfig = getElNodeConfigByType(nodeType);
      return elConfig?.edgeTags || [];
    },
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 获取已保存的边属性
        const edgeType = this.edgeData.properties?.edgeType || '';
        const exceptionHandling = this.edgeData.properties?.exceptionHandling || undefined;
        const breakLoop = this.edgeData.properties?.breakLoop || false;

        if (this.isSwitchNode()) {
          // Switch节点：如果有值则转为数组，没有值则为空数组
          this.formData.edgeType = edgeType ? [edgeType] : [];
        } else {
          // 其他节点：直接使用值，没有值则为 undefined
          this.formData.edgeType = edgeType || undefined;
        }

        // 设置异常处理配置
        this.formData.exceptionHandling = exceptionHandling;
        // 设置跳出循环体配置
        this.formData.breakLoop = breakLoop;

        // 其他表单项保持不变
        this.formData.code = this.edgeData.properties?.code || '';
      }
    },
  },
  methods: {
    isSwitchNode() {
      return this.edgeData?.sourceNode?.properties?.nodeType === 'SWITCH';
    },
    handleEdgeTypeChange(values) {
      // 只保留最后一个值
      if (values.length > 1) {
        this.formData.edgeType = [values[values.length - 1]];
      }
    },

    handleOk() {
      this.loading = true;
      this.$refs.form.validate((valid) => {
        if (valid) {
          try {
            const data = {
              ...this.edgeData,
              properties: {
                ...this.edgeData.properties,
                edgeType: this.isSwitchNode() ? this.formData.edgeType[0] : this.formData.edgeType,
                exceptionHandling: this.formData.exceptionHandling,
                breakLoop: this.formData.breakLoop,
              },
            };
            this.$emit('save', data);
            this.handleCancel();
          } catch (error) {
            this.$message.error('保存失败：' + error.message);
          }
        }
        this.loading = false;
      });
    },
    handleCancel() {
      this.formData.edgeType = undefined;
      this.formData.exceptionHandling = undefined;
      this.formData.breakLoop = false;
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style lang="scss" scoped>
.edge-modal {
  :deep(.ant-modal-body) {
    padding: 24px 24px 8px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 24px;
  }

  :deep(.ant-select) {
    .ant-select-selection {
      background-color: #fff;
    }
  }

  :deep(.ant-form-item-label) {
    text-align: right;
    padding-right: 12px;

    label {
      color: #333;
    }
  }
}
</style>
