<template>
  <div class="expression-preview" :class="{ 'is-collapsed': isCollapsed }">
    <div class="preview-header">
      <span class="title">EL表达式预览</span>
      <div class="header-actions">
        <!-- <a-tooltip title="复制表达式">
          <a-button type="link" class="action-btn" icon="copy" @click="handleCopy" />
        </a-tooltip> -->
        <!-- <a-tooltip title="LiteFlow EL表达式">
          <a-icon type="question-circle" class="help-icon" />
        </a-tooltip> -->

        <a-tooltip title="刷新">
          <a-button type="link" class="action-btn" icon="reload" @click="handleRefresh" />
        </a-tooltip>
        <a-tooltip :title="isCollapsed ? '展开' : '收起'">
          <a-button type="link" class="action-btn" :icon="isCollapsed ? 'down' : 'up'" @click="toggleCollapse" />
        </a-tooltip>
      </div>
    </div>
    <div class="expression-code" v-show="!isCollapsed">{{ expressionText }}</div>
  </div>
</template>

<script>
export default {
  name: 'ExpressionPreview',
  props: {
    expression: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isCollapsed: true,
    };
  },
  computed: {
    expressionText() {
      return this.formatExpression(this.expression);
    },
  },
  methods: {
    formatExpression(expression) {
      if (!expression) return '';

      // 定义需要处理换行的关键词
      const keywords = ['IF', 'SWITCH', 'FOR', 'WHILE', 'ITERATOR', 'TIMEOUT', 'PAR'];

      // 1. 在括号后和关键词后添加换行
      let formatted = expression;
      keywords.forEach((keyword) => {
        formatted = formatted.replace(new RegExp(`${keyword}\\(`, 'g'), `${keyword}(\n`);
      });
      formatted = formatted.replace(/\)/g, ')\n');

      // 2. 处理缩进
      const lines = formatted.split('\n');
      let indentLevel = 0;
      const indentSize = 2;
      formatted = lines
        .map((line) => {
          const trimmedLine = line.trim();

          // 减少缩进的情况
          if (trimmedLine.startsWith(')')) {
            indentLevel = Math.max(0, indentLevel - 1);
          }

          const indent = ' '.repeat(indentLevel * indentSize);
          const formattedLine = indent + trimmedLine;

          // 增加缩进的情况
          if (
            (keywords.some((keyword) => trimmedLine.startsWith(keyword)) && trimmedLine.includes('(')) ||
            (trimmedLine.includes('(') && !trimmedLine.includes(')'))
          ) {
            indentLevel++;
          }

          return formattedLine;
        })
        .join('\n');

      // 3. 移除多余的空行
      formatted = formatted
        .split('\n')
        .filter((line) => line.trim())
        .join('\n');

      return formatted;
    },

    handleRefresh() {
      this.$emit('refresh');
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
    },
  },
};
</script>

<style lang="scss" scoped>
.expression-preview {
  position: absolute;
  top: 100px;
  right: 40px;
  width: 360px;
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 16px rgba(0, 0, 0, 0.08);
  z-index: 100;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;

  &.is-collapsed {
    padding: 16px;
  }

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  .preview-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: #00b099;
      margin-right: 16px;
      letter-spacing: 0.5px;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-left: auto;

      .action-btn {
        padding: 4px;
        height: 32px;
        width: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;

        &:hover {
          background: rgba(0, 176, 153, 0.1);
          color: #00b099;
        }
      }

      .help-icon {
        color: #8c8c8c;
        font-size: 16px;
        cursor: pointer;
        transition: color 0.3s;

        &:hover {
          color: #00b099;
        }
      }
    }
  }

  .expression-code {
    background-color: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    font-size: 14px;
    line-height: 1.6;
    padding: 16px;
    height: 360px;
    margin: 0;
    overflow: auto;
    white-space: pre;
    word-wrap: normal;
    transition: all 0.3s ease;
    cursor: text;

    &:hover {
      border-color: #00b099;
      box-shadow: 0 0 0 2px rgba(0, 176, 153, 0.1);
    }
  }
}
</style>
