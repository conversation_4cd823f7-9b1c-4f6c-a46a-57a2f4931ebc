<template>
  <div class="node-panel">
    <div class="node-panel-header">
      <a-tooltip :title="chainName">
        <span class="chain-name">{{ chainName || '未命名' }}</span>
      </a-tooltip>
    </div>
    <a-collapse v-model="activeKeys" :bordered="false">
      <a-collapse-panel v-for="(nodes, type) in nodeTypes" :key="type" :header="getPanelHeader(type)">
        <a-tooltip v-for="(item, index) in nodes" :key="`${type}-${item.type}-${index}`" :title="item.label">
          <div
            class="node-item"
            @mousedown="onDragStart(item)"
            :class="{ 'function-node': type === 'function' }"
            :style="{ borderColor: item.properties.color }"
          >
            <div class="node-item-content">
              <div class="node-item-icon">
                <svg viewBox="0 0 1024 1024" width="20" height="20">
                  <path :d="getIconPath(item.properties.icon)" :fill="item.properties.color" />
                </svg>
              </div>
              <div class="node-item-label">{{ item.label }}</div>
            </div>
          </div>
        </a-tooltip>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<script>
import { nodeTypes, typeHeaderMap } from '../config/nodeTypes';
import { nodeIcons } from '../config/icons';

export default {
  name: 'NodePanel',
  props: {
    chainName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      nodeTypes,
      activeKeys: ['el', 'function'],
    };
  },
  methods: {
    onDragStart(item) {
      this.$emit('drag-start', item);
    },
    getPanelHeader(type) {
      return typeHeaderMap[type] || type;
    },
    getIconPath(type) {
      return nodeIcons[type];
    },
  },
};
</script>

<style lang="scss" scoped>
.node-panel {
  width: 280px;
  height: 100%;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  background: #fff;
  border-radius: 8px;
  margin-right: 24px;

  .node-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .chain-name {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }
  }

  :deep(.ant-collapse) {
    background: transparent;

    .ant-collapse-header {
      font-weight: 500;
      color: #333;
    }

    .ant-collapse-content {
      background: transparent;

      .ant-collapse-content-box {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
  }

  .node-item {
    display: flex;
    align-items: center;
    padding: 8px;
    height: 44px;
    width: calc(50% - 4px);
    margin-bottom: 0;
    cursor: move;
    border: 1px solid #eaeaea;
    border-radius: 4px;
    transition: all 0.2s ease-in-out;
    background: #fafafa;

    &.function-node {
      width: 44px;
      border-radius: 50%;
      padding: 4px;
      justify-content: center;

      .node-item-content {
        justify-content: center;
      }

      .node-item-icon {
        margin-right: 0;
      }

      .node-item-label {
        display: none;
      }
    }

    &:hover {
      background: #f0f7ff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
    }

    .node-item-content {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .node-item-icon {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 4px;
    }

    .node-item-label {
      flex: 1;
      font-size: 14px;
      color: #333;
      line-height: 1.5;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
