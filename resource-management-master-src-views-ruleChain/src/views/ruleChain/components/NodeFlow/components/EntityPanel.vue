<template>
  <div class="entity-panel">
    <div class="node-panel-header">
      <a-tooltip :title="chainName">
        <span class="chain-name">{{ chainName || '未命名' }}</span>
      </a-tooltip>
      <a-button v-if="isViewingHistory" size="small" icon="arrow-left" @click="handleBackToCurrent">
        返回当前版本
      </a-button>
    </div>

    <a-tabs v-model="activeTab" @change="handleTabChange" size="small">
      <a-tab-pane key="current" tab="当前版本">
        <a-table
          :columns="columns"
          :data-source="currentEntities"
          :pagination="currentEntities.length ? currentPagination : false"
          :loading="loading"
          @change="handleCurrentPaginationChange"
          :customRow="customRow"
          row-key="chainInstanceId"
        >
          <template slot="successFlag" slot-scope="text">
            <a-tag :color="text ? 'green' : 'red'">
              {{ text ? '成功' : '失败' }}
            </a-tag>
          </template>
          <template slot="action" slot-scope="text, record">
            <a-button size="small" icon="eye" @click="viewContext(record)">参数</a-button>
          </template>
        </a-table>
      </a-tab-pane>

      <a-tab-pane key="history" tab="历史版本">
        <a-table
          :columns="historyColumns"
          :data-source="versionData.history.versions"
          :pagination="versionData.history.versions.length ? historyVersionPagination : false"
          :loading="loading"
          :expanded-row-keys="expandedVersions"
          @change="handleHistoryVersionPaginationChange"
          @expand="handleExpand"
          row-key="version"
        >
          <template slot="action" slot-scope="text, record">
            <a-button size="small" icon="eye" @click="handleVersionView(record)">查看</a-button>
          </template>

          <a-table
            slot="expandedRowRender"
            slot-scope="record"
            :columns="entityColumns"
            :data-source="record.entities"
            :pagination="record.entities.length ? historyEntityPagination : false"
            :loading="record.loading"
            @change="(pagination) => handleHistoryEntityPaginationChange(pagination, record)"
            :customRow="customRow"
            size="small"
            row-key="chainInstanceId"
          >
            <template slot="successFlag" slot-scope="text">
              <a-tag :color="text ? 'green' : 'red'">
                {{ text ? '成功' : '失败' }}
              </a-tag>
            </template>
            <template slot="action" slot-scope="text, record">
              <a-button size="small" icon="eye" @click="viewContext(record)">参数</a-button>
            </template>
          </a-table>
        </a-table>
      </a-tab-pane>
    </a-tabs>

    <a-modal
      v-model="debugModalVisible"
      title="调试信息"
      @cancel="closeModal"
      :zIndex="9999"
      :footer="null"
      width="60%"
    >
      <json-viewer :value="debugData" :expand-depth="5" copyable boxed sort theme="jv-light" />
    </a-modal>
  </div>
</template>

<script>
import moment from 'moment';
import JsonViewer from 'vue-json-viewer';
import 'vue-json-viewer/style.css';

export default {
  name: 'EntityPanel',
  components: {
    JsonViewer,
  },
  props: {
    chainId: {
      type: String,
      default: '',
    },
    chainName: {
      type: String,
      default: '',
    },
    version: {
      type: Number,
    },
  },
  data() {
    return {
      // 调试相关
      debugData: {},
      debugModalVisible: false,

      // 当前选中的tab
      activeTab: 'current',

      // 加载状态
      loading: false,

      // 是否在查看历史版本
      isViewingHistory: false,

      // 版本数据
      versionData: {
        current: {
          versions: [], // 当前版本列表
        },
        history: {
          versions: [], // 历史版本列表
        },
      },

      // 当前版本实例分页
      currentPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条`,
      },

      // 历史版本分页
      historyVersionPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条`,
      },

      // 历史版本实例分页
      historyEntityPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条`,
      },

      // 展开的版本记录
      expandedVersions: [],

      // 当前选中的实例ID
      selectedChainInstanceId: '',

      // 表格列定义
      columns: [
        {
          title: '链实例ID',
          dataIndex: 'chainInstanceId',
          key: 'chainInstanceId',
          width: 180,
        },
        {
          title: '开始时间',
          dataIndex: 'instanceDate',
          key: 'instanceDate',
          customRender: ({ text }) => {
            return moment(text).format('YYYY-MM-DD HH:mm:ss');
          },
        },
        {
          title: '状态',
          dataIndex: 'successFlag',
          key: 'successFlag',
          scopedSlots: { customRender: 'successFlag' },
        },
        {
          title: '操作',
          key: 'action',
          scopedSlots: { customRender: 'action' },
        },
      ],
      historyColumns: [
        {
          title: '版本号',
          dataIndex: 'version',
          key: 'version',
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'createTime',
          customRender: ({ text }) => {
            return moment(text).format('YYYY-MM-DD HH:mm:ss');
          },
        },
        {
          title: '创建人',
          dataIndex: 'createUser',
          key: 'createUser',
        },
        {
          title: '操作',
          key: 'action',
          scopedSlots: { customRender: 'action' },
        },
      ],
      entityColumns: [
        {
          title: '链实例ID',
          dataIndex: 'chainInstanceId',
          key: 'chainInstanceId',
          width: 120,
        },
        {
          title: '开始时间',
          dataIndex: 'instanceDate',
          key: 'instanceDate',
          customRender: ({ text }) => {
            return moment(text).format('YYYY-MM-DD HH:mm:ss');
          },
        },
        {
          title: '状态',
          dataIndex: 'successFlag',
          key: 'successFlag',
          scopedSlots: { customRender: 'successFlag' },
        },
        {
          title: '操作',
          key: 'action',
          scopedSlots: { customRender: 'action' },
        },
      ],
    };
  },
  computed: {
    // 获取当前版本的实例列表
    currentEntities() {
      const currentVersion = this.versionData.current.versions[0];
      return currentVersion ? currentVersion.entities : [];
    },
  },
  methods: {
    moment,

    // 获取当前版本实例列表
    async getCurrentVersionEntities() {
      this.loading = true;
      try {
        const [res] = await this.$post(this.api.rule_chain_entity, {
          chainId: this.chainName,
          chainVersion: this.version,
          pageNum: this.currentPagination.current,
          pageSize: this.currentPagination.pageSize,
        });
        if (res && res.success) {
          // 将数据格式化为与历史版本一致的结构
          this.versionData.current.versions = [
            {
              version: this.version,
              entities: res.data || [],
              loading: false,
            },
          ];
          this.currentPagination.total = res.total;

          // 设置默认选中第一个实例
          if (res.data && res.data.length > 0 && !this.selectedChainInstanceId) {
            this.selectedChainInstanceId = res.data[0].chainInstanceId;
          }
        }
      } catch (error) {
        this.$message.error('获取实例列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 获取历史版本列表
    async getHistoryVersions() {
      this.loading = true;
      try {
        const [res] = await this.$post(this.api.rule_chain_version_list, {
          chainId: this.chainName,
          pageNum: this.historyVersionPagination.current,
          pageSize: this.historyVersionPagination.pageSize,
        });
        if (res && res.success) {
          this.versionData.history.versions = (res.data || []).map((version) => ({
            ...version,
            entities: [],
            loading: false,
          }));
          this.historyVersionPagination.total = res.total;

          // 自动展开第一个版本
          if (res.data && res.data.length > 0) {
            this.expandedVersions = [res.data[0].version];
            this.getVersionEntities(res.data[0].version);
          }
        }
      } catch (error) {
        this.$message.error('获取历史版本列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 获取历史版本的实例列表
    async getVersionEntities(version) {
      const targetVersion = this.versionData.history.versions.find((v) => v.version === version);
      if (!targetVersion) return;

      targetVersion.loading = true;
      try {
        const [res] = await this.$post(this.api.rule_chain_entity, {
          chainId: this.chainName,
          chainVersion: version,
          pageNum: this.historyEntityPagination.current,
          pageSize: this.historyEntityPagination.pageSize,
        });
        if (res && res.success) {
          targetVersion.entities = res.data || [];
          this.historyEntityPagination.total = res.total;

          // 自动选中展开版本的第一个实例
          if (res.data && res.data.length > 0) {
            this.selectedChainInstanceId = res.data[0].chainInstanceId;
          }
        }
      } catch (error) {
        this.$message.error('获取版本实例列表失败');
      } finally {
        targetVersion.loading = false;
      }
    },

    // 处理当前版本分页变化
    handleCurrentPaginationChange(pagination) {
      this.currentPagination = { ...this.currentPagination, ...pagination };
      this.getCurrentVersionEntities();
    },

    // 处理历史版本分页变化
    handleHistoryVersionPaginationChange(pagination) {
      this.historyVersionPagination = { ...this.historyVersionPagination, ...pagination };
      this.expandedVersions = []; // 清空展开的版本
      this.getHistoryVersions();
    },

    // 处理历史版本实例分页变化
    handleHistoryEntityPaginationChange(pagination, record) {
      this.historyEntityPagination = { ...this.historyEntityPagination, ...pagination };
      this.getVersionEntities(record.version);
    },

    // 处理Tab切换
    handleTabChange(activeKey) {
      this.activeTab = activeKey;
      this.isViewingHistory = false;
      this.selectedChainInstanceId = ''; // 清空选中状态

      if (activeKey === 'current') {
        this.currentPagination.current = 1;
        this.getCurrentVersionEntities();
        // 切换到当前版本时，通知父组件
        this.$emit('version-view', {
          isHistoryVersion: false,
        });
      } else {
        this.historyVersionPagination.current = 1;
        this.expandedVersions = [];
        this.getHistoryVersions();
      }
    },

    // 处理展开/收起
    handleExpand(expanded, record) {
      if (expanded) {
        this.expandedVersions = [record.version];
        this.historyEntityPagination.current = 1;
        this.selectedChainInstanceId = ''; // 清空当前选中状态
        this.getVersionEntities(record.version);
      } else {
        this.expandedVersions = [];
        this.selectedChainInstanceId = ''; // 收起时也清空选中状态
      }
    },

    // 查看调试信息
    async viewContext(record) {
      const [res] = await this.$post(this.api.rule_chain_debug, {
        chainId: this.chainName,
        chainInstanceId: record.chainInstanceId,
      });
      if (res && res.success) {
        console.error(1111);
        this.debugData = res.data.params ? JSON.parse(res.data.params) : {};
        this.debugModalVisible = true;
      } else {
        this.$message.error('获取调试信息失败');
      }
    },

    // 查看实例详情
    handleView(record) {
      console.log('查看实例详情', record);
    },

    // 查看版本详情
    async handleVersionView(record) {
      const { id } = record;
      const [res] = await this.$get(this.api.rule_chain_version_detail, {
        id,
      });
      if (res && res.success) {
        this.isViewingHistory = true;
        // 发送事件给父组件
        this.$emit('version-view', {
          ...res.data,
          isHistoryVersion: true,
          versionInfo: record,
        });
      }
    },

    // 返回当前版本
    handleBackToCurrent() {
      this.isViewingHistory = false;
      this.$emit('version-view', {
        isHistoryVersion: false,
      });
    },

    // 关闭调试弹窗
    closeModal() {
      this.debugModalVisible = false;
      this.debugData = {};
    },

    // 自定义行属性
    customRow(record) {
      return {
        class: {
          'selected-row': record.chainInstanceId === this.selectedChainInstanceId,
        },
        on: {
          click: () => {
            this.selectedChainInstanceId = record.chainInstanceId;
          },
        },
      };
    },
  },
  mounted() {
    // 确保初始化时是当前版本tab
    this.activeTab = 'current';
    this.handleTabChange('current');
  },
  watch: {
    version: {
      handler(newVal) {
        if (this.activeTab === 'current' && newVal) {
          this.getCurrentVersionEntities();
          // 版本变化时关闭历史版本查看
          this.isViewingHistory = false;
        }
      },
      immediate: true,
    },
    selectedChainInstanceId: {
      handler(newVal) {
        this.$emit('selected-chain-instance-id', newVal);
      },
    },
  },
};
</script>

<style lang="scss" scoped>
.entity-panel {
  width: 560px;
  height: 100%;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  background: #fff;
  border-radius: 8px;
  margin-right: 24px;

  .node-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .chain-name {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }
  }

  :deep(.ant-table-small) {
    margin: -8px;
  }

  // 优化嵌套表格样式
  :deep(.ant-table) {
    // 主表格样式
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 500;
    }

    // 表格行hover效果
    .ant-table-tbody > tr:hover > td {
      background: #f5f5f5;
    }

    // 展开图标样式
    .ant-table-row-expand-icon {
      border-radius: 2px;
      margin-right: 8px;

      &:hover {
        background: #f0f0f0;
      }
    }

    // 展开行样式
    .ant-table-expanded-row {
      background: #fafafa;

      // 内嵌表格样式
      .ant-table-small {
        margin: 0;
        border: 1px solid #f0f0f0;
        border-radius: 4px;

        .ant-table-thead > tr > th {
          background: #f5f5f5;
          padding: 8px 16px;
        }

        .ant-table-tbody > tr > td {
          padding: 8px 16px;
        }
      }
    }

    // 分页器样式
    .ant-pagination {
      margin: 16px 0 0;
      padding: 0;
      text-align: right;
    }

    // 选中行样式
    .selected-row {
      background-color: #e6f7ff;

      &:hover > td {
        background-color: #e6f7ff !important;
      }
    }
  }

  // 表格操作按钮样式
  :deep(.ant-btn-link) {
    padding: 0 8px;
    height: 24px;
    line-height: 24px;

    &:hover {
      background: rgba(0, 0, 0, 0.02);
    }
  }

  // 状态标签样式
  :deep(.ant-tag) {
    margin-right: 0;
    min-width: 48px;
    text-align: center;
  }
}
</style>
