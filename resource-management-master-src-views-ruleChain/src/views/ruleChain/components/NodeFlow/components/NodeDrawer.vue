<template>
  <a-drawer :title="nodeData.properties?.name || '节点配置'" :visible="value" @close="onClose" width="80%">
    <a-tabs v-model="activeTab" @change="handleTabChange">
      <a-tab-pane key="basic" tab="基础信息">
        <a-form-model
          ref="formRef"
          :model="formData"
          :rules="rules"
          :label-col="{ span: 2 }"
          :wrapper-col="{ span: 22 }"
        >
          <a-form-model-item label="节点类型">
            <a-tag :color="nodeData.properties?.color">
              {{ nodeData.properties?.nodeType || '基础节点' }}
            </a-tag>
          </a-form-model-item>

          <a-form-model-item label="节点名称" prop="name">
            <a-input v-model="formData.name" placeholder="请输入节点名称" @change="handleNameChange" />
          </a-form-model-item>

          <component
            :is="currentFormComponent"
            :formData="formData"
            @update:formData="updateFormData"
            ref="nodeFormComponent"
          />

          <a-form-model-item label="描述">
            <a-textarea
              v-model="formData.description"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入节点描述信息"
            />
          </a-form-model-item>
        </a-form-model>
      </a-tab-pane>

      <a-tab-pane v-if="isDebug" key="debug" tab="调试信息">
        <a-spin :spinning="loading">
          <a-empty v-if="!debugList" description="暂无调试信息" />
          <debug-table v-else :debug-list="debugList" />
        </a-spin>
      </a-tab-pane>
    </a-tabs>

    <div class="drawer-footer">
      <a-button :style="{ marginRight: '8px' }" @click="onClose">取消</a-button>
      <a-button type="primary" @click="handleSubmit">确定</a-button>
    </div>
  </a-drawer>
</template>

<script>
import { mockDebugList } from '../mock';
import DebugTable from './DebugTable.vue';

export default {
  name: 'NodeDrawer',
  components: {
    DebugTable,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    nodeData: {
      type: Object,
      default: () => ({}),
    },
    // // 是否是调试模式
    isDebug: {
      type: Boolean,
      default: false,
    },
    // 规则链id
    chainId: {
      type: String,
      default: '',
    },
    // 规则链实例id
    chainInstanceId: {
      type: String,
      default: '',
    },
    // 规则链名称
    chainName: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      activeTab: 'basic',
      debugList: null,
      formData: {
        name: '',
        description: '',
        timeout: 3,
      },
      rules: {
        name: [{ required: true, message: '请输入节点名称', trigger: 'blur' }],
      },
      loading: false,
      currentFormComponent: null,
    };
  },
  computed: {
    formComponentName() {
      const nodeType = this.nodeData.properties?.nodeType;
      return nodeType;
    },
  },
  watch: {
    formComponentName: {
      immediate: true,
      handler(nodeType) {
        if (nodeType) {
          const componentName = `${nodeType}Form`;
          import(`./nodeForms/${componentName}.vue`)
            .then((module) => {
              this.currentFormComponent = module.default;
            })
            .catch((error) => {
              console.error(`Failed to load form component for ${nodeType}:`, error);
              this.currentFormComponent = null;
            });
        } else {
          this.currentFormComponent = null;
        }
      },
    },
    value(newVal) {
      if (newVal) {
        if (this.nodeData?.type === 'start-node') {
          this.$message.warning('开始节点不可编辑');
          this.$emit('close');
          return;
        }
        console.log(this.nodeData, 'nodeData');
        // 使用深拷贝初始化表单数据
        this.formData = {
          description: this.nodeData.description || '',
          name: this.getNodeName(),
          ...JSON.parse(JSON.stringify(this.nodeData.properties || {})), // 深拷贝properties
        };
      } else {
        this.activeTab = 'basic';
      }
    },
  },
  methods: {
    // 获取节点名称
    getNodeName() {
      // if (this.nodeData.properties.nodeType === 'TIMEOUT') {
      //   return '超时' + this.nodeData.properties.timeout + '秒';
      // }
      // if (this.nodeData.properties.nodeType === 'PAR') {
      //   return '并行' + (this.nodeData.properties.parType === 'START' ? '开始' : '结束');
      // }
      return this.nodeData.properties.name;
    },

    updateFormData(newData) {
      this.formData = { ...this.formData, ...newData };
    },
    handleError(error) {
      this.$message.error(error);
    },
    generateRandomName() {
      // 生成6位随机字符串（数字和字母组合）
      const randomStr = String.fromCharCode(65 + Math.floor(Math.random() * 26));
      return `${randomStr}`;
    },
    async handleSubmit() {
      try {
        // 验证整个表单
        await this.$refs.formRef.validate();

        // 获取表单数据并深拷贝
        const completeFormData = {
          ...JSON.parse(JSON.stringify(this.nodeData.properties || {})), // 深拷贝原有属性
          ...JSON.parse(JSON.stringify(this.formData)), // 深拷贝新的属性
          nodeType: this.nodeData.properties?.nodeType,
          name: this.formData.name,
          description: this.formData.description,
          saved: true, // 标记为已保存
        };

        // 保存节点数据
        this.$emit('save', completeFormData);
        this.onClose();
      } catch (error) {
        console.warn('表单验证失败');
      }
    },
    onClose() {
      this.$refs.formRef.resetFields();
      this.$emit('close');
    },
    // 输入时自动过滤特殊字符
    handleNameChange(e) {
      // 只保留中文、英文、数字和下划线
      this.formData.name = this.formData.name.replace(/[^a-zA-Z0-9\u4e00-\u9fa5_]/g, '');
    },
    handleTabChange(key) {
      if (key === 'debug' && this.isDebug) {
        // 获取单个节点调试信息
        this.getSingleNodeDebugInfo();
      }
    },
    async getSingleNodeDebugInfo() {
      try {
        if (!this.chainInstanceId) {
          this.$message.error('请先选择实例');
          return;
        }
        this.loading = true;
        const [res] = await this.$post(this.api.rule_chain_node_debug, {
          chainId: this.chainName,
          chainInstanceId: this.chainInstanceId,
          nodeId: this.nodeData.properties.scriptName,
        });
        this.loading = false;
        this.debugList = res.data || [];
      } catch (error) {
        this.$message.error('获取调试信息失败');
      }
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #f0f0f0;
  padding: 12px 24px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}
</style>
