<template>
  <a-modal title="自测" :visible="visible" @cancel="handleCancel" :width="800" :footer="null">
    <div class="code-block-container">
      <div class="mode-indicator" :class="{ 'code-mode': form.params, 'api-mode': !form.params }">
        <a-tooltip :title="getTooltipText">
          <div class="mode-content">
            <div class="mode-icon">
              <a-icon :type="form.params ? 'code' : 'api'" />
            </div>
            <span class="mode-text">{{ form.params ? '自定义代码块模式' : '实例上下文模式' }}</span>
          </div>
        </a-tooltip>
      </div>
    </div>

    <a-form-model ref="form" :model="form" :rules="rules" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
      <a-form-model-item label="代码块" prop="params">
        <sm-code-editor v-model="form.params" mode="json" :validate="false" :showError="false" />
      </a-form-model-item>

      <a-form-model-item :wrapper-col="{ span: 22, offset: 2 }">
        <a-button type="primary" @click="handleTest">开始自测</a-button>
      </a-form-model-item>
    </a-form-model>

    <div v-if="testResults.length > 0" class="test-results">
      <a-card v-for="(result, index) in testResults" :key="index" :class="['result-card', { success: result.success }]">
        <template #title>
          <div class="card-title">
            <div class="title-left">
              <a-icon :type="result.success ? 'check-circle' : 'warning'" />
              <span>{{ result.title }}</span>
            </div>
            <a-tooltip title="复制结果">
              <a-button type="link" class="copy-btn" @click="() => copyContent(result)">
                <a-icon type="copy" />
              </a-button>
            </a-tooltip>
          </div>
        </template>
        <a-descriptions :column="1">
          <a-descriptions-item v-if="result.data">
            <pre class="result-content">{{ result.data }}</pre>
          </a-descriptions-item>
          <a-descriptions-item v-else>
            <div class="error-message">{{ result.error || '测试失败' }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import SmCodeEditor from '@/components/CodeEditor/index.vue';

export default {
  name: 'TestModal',
  components: {
    SmCodeEditor,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    selectionInfo: {
      type: Object,
      default: () => ({}),
    },
    chainInstanceId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      form: {
        params: '',
      },
      rules: {
        params: [],
      },
      testResults: [],
    };
  },
  watch: {
    visible(val) {
      if (!val) {
        this.resetForm();
      }
    },
  },
  methods: {
    handleCancel() {
      this.$emit('update:visible', false);
      this.resetForm();
    },
    resetForm() {
      this.form = {
        params: '',
      };
      this.testResults = [];
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    async handleTest() {
      this.testResults = [];
      let testParams = {};
      if (this.form.params) {
        testParams = this.form;
      } else {
        if (!this.chainInstanceId) {
          this.$message.error('实例上下文模式下，实例参数不能为空');
          return;
        }
        testParams = {
          chainInstanceId: this.chainInstanceId,
        };
      }

      const [res, err] = await this.$post(this.api.rule_chain_node_self_test, {
        ...testParams,
        ...this.selectionInfo,
      });

      const [res2, err2] = await this.$post(this.api.rule_chain_el_preview, {
        ...this.selectionInfo,
      });

      // 处理测试结果
      this.testResults = [
        {
          title: '自测结果',
          success: res && res.success,
          data: res?.data,
          error: err?.message,
        },
        {
          title: '表达式预览',
          success: res2 && res2.success,
          data: res2?.data,
          error: err2?.message,
        },
      ];
    },
    copyContent(result) {
      if (!result) return;
      const content = result.data ? JSON.stringify(result.data, null, 2) : result.error || '测试失败';
      const textarea = document.createElement('textarea');
      textarea.value = content;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
      this.$message.success('复制成功');
    },
  },
  computed: {
    getTooltipText() {
      return this.form.params
        ? '当前使用自定义代码块进行测试，系统将执行您输入的代码内容'
        : '当前使用实例上下文进行测试，系统将使用当前实例的配置进行测试';
    },
  },
};
</script>

<style lang="scss" scoped>
.result-container {
  margin-top: 24px;
  border-top: 1px solid #e8e8e8;
  padding-top: 16px;

  .result-content {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .result-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .ant-tag {
        margin: 0 8px;
      }
    }
  }
}

.code-block-container {
  position: relative;
  margin-bottom: 16px;

  .mode-indicator {
    display: block;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;

    &.code-mode {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;

      .mode-icon {
        color: #1890ff;
      }

      .mode-text {
        color: #1890ff;
      }
    }

    &.api-mode {
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;

      .mode-icon {
        color: #52c41a;
      }

      .mode-text {
        color: #52c41a;
      }
    }

    .mode-content {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .mode-icon {
        margin-right: 8px;

        .anticon {
          font-size: 18px;
          transition: all 0.3s ease;
        }
      }

      .mode-text {
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
      }
    }
  }
}

.tooltip-content {
  p {
    margin: 0;
    line-height: 1.6;

    &:not(:last-child) {
      margin-bottom: 4px;
    }
  }
}

.test-results {
  margin-top: 24px;

  .result-card {
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;

    &.success {
      border-color: #b7eb8f;

      :deep(.ant-card-head) {
        background: #f6ffed;
        border-bottom-color: #b7eb8f;
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    .card-title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title-left {
        display: flex;
        align-items: center;

        .anticon {
          margin-right: 8px;
          font-size: 16px;

          &.anticon-check-circle {
            color: #52c41a;
          }

          &.anticon-warning {
            color: #ff4d4f;
          }
        }
      }

      .copy-btn {
        padding: 0;
        height: auto;

        .anticon {
          font-size: 14px;
        }

        &:hover {
          color: #40a9ff;
        }
      }
    }

    .result-content {
      margin: 0;
      padding: 12px;
      background: #fafafa;
      border-radius: 4px;
      font-family: monospace;
      white-space: pre-wrap;
      word-break: break-all;
    }

    .error-message {
      color: #ff4d4f;
    }
  }
}
</style>
