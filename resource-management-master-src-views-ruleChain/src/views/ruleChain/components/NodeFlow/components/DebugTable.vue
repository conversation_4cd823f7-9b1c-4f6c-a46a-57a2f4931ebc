<template>
  <div class="debug-table">
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="{ pageSize: 10 }"
      size="small"
      :scroll="{ x: 1000 }"
    >
      <span slot="action" slot-scope="text, record">
        <a-button icon="eye" size="small" @click="showModal(record)">参数</a-button>
      </span>
    </a-table>

    <a-modal v-model="modalVisible" title="数据值" @cancel="closeModal" :zIndex="9999" :footer="null" width="60%">
      <div class="data-detail">
        <json-viewer
          :value="currentRecord?.dataValue ? JSON.parse(currentRecord.dataValue) : {}"
          :expand-depth="3"
          copyable
          boxed
          sort
          theme="jv-light"
        />
      </div>
    </a-modal>
  </div>
</template>

<script>
import JsonViewer from 'vue-json-viewer';
import 'vue-json-viewer/style.css';

export default {
  name: 'DebugTable',
  components: {
    JsonViewer,
  },
  props: {
    debugList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      columns: [
        {
          title: '数据编码',
          dataIndex: 'dataCode',
          width: 120,
        },
        {
          title: '数据类型',
          dataIndex: 'dataType',
          width: 100,
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          width: 160,
        },
        {
          title: '结束时间',
          dataIndex: 'endTime',
          width: 160,
        },
        {
          title: '执行结果',
          dataIndex: 'successFlag',

          customRender: (text) => {
            return text ? '成功' : '失败';
          },
        },
        {
          title: '错误信息',
          dataIndex: 'errorMsg',
        },
        {
          title: '操作',
          width: 120,
          scopedSlots: { customRender: 'action' },
        },
      ],
      modalVisible: false,
      currentRecord: null,
    };
  },
  computed: {
    dataSource() {
      return this.debugList.map((item, index) => ({
        ...item,
        key: index,
      }));
    },
  },
  methods: {
    showModal(record) {
      try {
        // 尝试解析JSON字符串
        if (record.dataValue) {
          JSON.parse(record.dataValue);
        }
        this.currentRecord = record;
        this.modalVisible = true;
      } catch (e) {
        this.$message.error('数据格式不是有效的JSON');
      }
    },
    closeModal() {
      this.modalVisible = false;
      this.currentRecord = null;
    },
  },
};
</script>

<style lang="scss" scoped></style>
