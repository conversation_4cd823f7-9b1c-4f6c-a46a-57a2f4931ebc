<template>
  <div class="toolbar">
    <div class="toolbar-item">
      <a-button
        :icon="isSelecting ? 'close' : 'select'"
        :type="isSelecting ? 'default' : 'primary'"
        size="small"
        @click="handleClick"
        class="mr-8"
      >
        {{ isSelecting ? '关闭框选' : '框选' }}
      </a-button>

      <a-button
        :icon="isDebug ? 'close' : 'select'"
        :type="isDebug ? 'default' : 'primary'"
        size="small"
        @click="handleDebug"
        class="mr-8"
      >
        {{ isDebug ? '退出调试' : '调试' }}
      </a-button>

      <a-dropdown-button class="toolbar-group" size="small" type="primary">
        <template #icon><a-icon type="tool" /></template>
        工具箱
        <template #overlay>
          <a-menu>
            <a-menu-item key="version" @click="handleVersionControl">
              <a-icon type="history" />
              <span>版本控制</span>
            </a-menu-item>
            <a-menu-item key="export" @click="handleExport">
              <a-icon type="export" />
              <span>导出</span>
            </a-menu-item>
            <a-menu-item key="import" @click="handleImport">
              <a-icon type="import" />
              <span>导入</span>
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="toggleExpression" @click="handleToggleExpression">
              <a-icon :type="showExpressionPreview ? 'eye-invisible' : 'eye'" />
              <span>{{ showExpressionPreview ? '隐藏表达式预览' : '显示表达式预览' }}</span>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown-button>
    </div>
    <input type="file" ref="fileInput" accept=".json" style="display: none" @change="handleFileSelected" />
  </div>
</template>

<script>
export default {
  name: 'FlowToolbar',
  props: {
    isSelecting: {
      type: Boolean,
      default: false,
    },
    showExpressionPreview: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isDebug: false,
    };
  },
  mounted() {
    this.isDebug = localStorage.getItem('isDebug') === 'true';
  },
  methods: {
    handleClick() {
      this.$emit('selection-change');
    },
    handleDebug() {
      if (!this.isDebug) {
        this.$confirm({
          title: '调试确认',
          content: '确定要进入调试模式吗？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            this.isDebug = true;
            localStorage.setItem('isDebug', 'true');
            this.$emit('debug');
          },
        });
      } else {
        this.$confirm({
          title: '调试确认',
          content: '确定要退出调试模式吗？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            this.isDebug = false;
            localStorage.setItem('isDebug', 'false');
            this.$emit('debug-close');
          },
        });
      }
    },

    handleVersionControl() {
      this.$emit('version-control');
    },
    handleExport() {
      this.$emit('export');
    },
    handleImport() {
      this.$refs.fileInput.click();
    },
    handleFileSelected(event) {
      const file = event.target.files[0];
      if (!file) return;

      if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
        this.$message.error('请选择JSON格式的文件');
        event.target.value = '';
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const jsonData = JSON.parse(e.target.result);
          if (!jsonData.nodes || !jsonData.edges) {
            throw new Error('无效的画布数据格式');
          }
          this.$emit('import', jsonData);
        } catch (error) {
          this.$message.error('文件格式错误：' + error.message);
        }
        event.target.value = '';
      };
      reader.onerror = () => {
        this.$message.error('文件读取失败');
        event.target.value = '';
      };
      reader.readAsText(file);
    },
    handleToggleExpression() {
      this.$emit('toggle-expression');
    },
  },
};
</script>

<style lang="scss" scoped>
.toolbar {
  position: absolute;
  top: 24px;
  left: 24px;
  right: 0;
  z-index: 1000;
}
</style>
