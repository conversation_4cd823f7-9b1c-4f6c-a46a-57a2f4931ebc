<template>
  <div>
    <a-modal
      v-model="visible"
      title="规则链版本"
      @cancel="closeModal"
      :zIndex="9999"
      :footer="null"
      width="80%"
      :destroyOnClose="true"
    >
      <div class="version-container">
        <!-- 版本列表 -->
        <a-table
          :columns="columns"
          :data-source="versionList"
          :pagination="pagination"
          @change="handleTableChange"
          rowKey="id"
        >
          <template slot="action" slot-scope="text, record">
            <a-space>
              <a @click="handleCompare(record)">
                <a-icon type="swap" />
                与当前比较
              </a>
              <a @click="handleRestore(record)">
                <a-icon type="rollback" />
                还原版本
              </a>
            </a-space>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 添加JSON对比Modal -->
    <a-modal
      v-model="compareModalVisible"
      title="版本对比"
      @cancel="closeCompareModal"
      :zIndex="10000"
      :footer="null"
      width="90%"
      :destroyOnClose="true"
    >
      <div class="compare-container">
        <json-diff :jsonSourceLeft="currentVersionData" :jsonSourceRight="rollbackVersionData" :showHeading="true" />
      </div>
    </a-modal>
  </div>
</template>

<script>
import JsonDiff from 'vue-json-diff';

export default {
  name: 'VersionModal',
  components: {
    JsonDiff,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    chainId: {
      type: String,
      default: '',
    },
    chainName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      versionList: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条`,
      },
      columns: [
        {
          title: '版本号',
          dataIndex: 'version',
          key: 'version',
          minWidth: 200,
        },
        {
          title: '版本ID',
          dataIndex: 'id',
          key: 'id',
          minWidth: 200,
        },
        {
          title: '创建人',
          dataIndex: 'createUser',
          key: 'createUser',
          minWidth: 200,
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'createTime',
          width: 180,
          sorter: true,
        },
        {
          title: '操作',
          key: 'action',
          width: 300,
          scopedSlots: { customRender: 'action' },
        },
      ],

      // 当前版本数据
      currentVersionData: {},
      // 回滚版本数据
      rollbackVersionData: {},
      compareModalVisible: false,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadVersionList();

        // 初始化分页数据
        this.pagination.current = 1;
        this.pagination.pageSize = 10;
        this.pagination.total = 0;

        // 清空版本列表
        this.versionList = [];

        // 清空对比数据
        this.currentVersionData = {};
        this.rollbackVersionData = {};
        this.compareModalVisible = false;
      }
    },
  },
  methods: {
    closeModal() {
      this.$emit('update:visible', false);
    },
    async loadVersionList() {
      const [res, err] = await this.$post(this.api.rule_chain_version_list, {
        chainId: this.chainName,
        pageCount: this.pagination.current,
        pageSize: this.pagination.pageSize,
      });
      this.versionList = res.data;
      this.pagination.total = res.total;
    },
    handleSearch() {
      this.pagination.current = 1;
      this.loadVersionList();
    },
    handleTableChange(pagination, filters, sorter) {
      this.pagination = { ...this.pagination, ...pagination };
      this.loadVersionList();
    },
    async handleCompare(record) {
      try {
        const [res, err] = await this.$get(this.api.rule_chain_version_compare, {
          chainId: this.chainName,
          historyId: record.id,
        });
        if (err) {
          return;
        }
        const { currentInfo, rollbackInfo } = res?.data || {};
        this.currentVersionData = currentInfo;
        this.rollbackVersionData = rollbackInfo;
        this.compareModalVisible = true;
      } catch (error) {
        console.error('加载版本对比数据失败:', error);
      }
    },
    async handleRestore(record) {
      console.log('record', record);
      this.$confirm({
        title: '确认还原',
        content: `确定要还原到版本 "${record.version}" 吗？`,
        okText: '确定',
        cancelText: '取消',
        zIndex: 10000,
        onOk: async () => {
          const [res, err] = await this.$get(this.api.rule_chain_version_rollback, {
            chainId: this.chainName,
            historyId: record.id,
          });
          if (err) {
            return;
          }
          this.$message.success('版本还原成功，系统将自动刷新页面...');
          this.closeModal();
          setTimeout(() => {
            this.$emit('restore', record);
          }, 2000);
        },
      });
    },
    closeCompareModal() {
      this.compareModalVisible = false;
      this.rollbackVersionData = {};
      this.currentVersionData = {};
    },
    handleNodeClick(node) {
      // 处理节点点击事件，可以高亮显示对应的差异
      console.log('Clicked node:', node);
    },
  },
};
</script>

<style lang="less" scoped>
.version-container {
  .version-id {
    display: flex;
    align-items: center;
  }
}

.compare-container {
  height: calc(100vh - 300px);
  overflow: auto;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
}
</style>
