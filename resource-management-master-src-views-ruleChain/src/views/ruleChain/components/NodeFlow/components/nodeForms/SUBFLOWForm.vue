<template>
  <div>
    <a-form-model-item label="子规则链" prop="subChainId" :rules="[{ required: true, message: '请选择子规则链' }]">
      <a-select
        v-model="formData.subChainId"
        :options="chainList"
        show-search
        :filter-option="false"
        placeholder="请选择子规则链"
        @search="handleSearch"
        @change="handleChange"
      />
    </a-form-model-item>
  </div>
</template>

<script>
import { debounce } from 'lodash';
export default {
  name: 'SUBFLOWForm',
  props: {
    formData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      chainList: [],
    };
  },
  mounted() {
    this.getChainList();
  },
  methods: {
    async getChainList(searchValue = '') {
      const [res, err] = await this.$post(this.api.rule_chain_all, {
        chainName: searchValue,
      });
      if (err) {
        return;
      }
      this.chainList = (res?.data || []).map((item) => ({
        label: item.chainName,
        value: item.id,
      }));
    },
    handleSearch: debounce(function (value) {
      this.getChainList(value);
    }, 300),
    handleChange(value) {
      const chainName = this.chainList.find((item) => item.value === value)?.label;
      this.formData.scriptName = chainName;
    },
  },
};
</script>
