<template>
  <div class="logic-expression">
    <!-- 逻辑类型选择 -->
    <div class="form-section">
      <a-form-model-item v-if="needLogicType" label="逻辑类型" required>
        <div class="logic-header">
          <a-select v-model="currentValue.type" style="width: 200px">
            <a-select-option v-if="!isRoot" value="NODE">节点</a-select-option>
            <a-select-option value="AND">与运算(AND)</a-select-option>
            <a-select-option value="OR">或运算(OR)</a-select-option>
            <a-select-option value="NOT">非运算(NOT)</a-select-option>
          </a-select>
          <a-button type="link" class="add-btn" @click="addChild" :disabled="currentValue.type === 'NODE'">
            <a-icon type="plus-circle" theme="filled" />添加子表达式
          </a-button>
        </div>
      </a-form-model-item>
    </div>

    <!-- 子表达式区域 -->
    <div v-if="currentValue.children.length > 0" class="logic-children">
      <div v-for="(child, index) in currentValue.children" :key="index" class="logic-child">
        <div class="logic-child-header">
          <div class="header-left">
            <span class="child-tag">子表达式</span>
            <span class="child-index">{{ index + 1 }}</span>
          </div>
          <a-button type="link" class="delete-btn" @click="removeChild(index)"> <a-icon type="delete" />删除 </a-button>
        </div>
        <logic-expression v-model="currentValue.children[index]" :is-root="false" :need-logic-type="true" />
      </div>
    </div>

    <!-- 节点输入区域 -->
    <div v-if="currentValue.type === 'NODE' || !currentValue.children.length" class="form-section">
      <a-form-model-item label="名称" required>
        <a-input v-model="currentValue.name" />
      </a-form-model-item>

      <common-code-form :formData="currentValue" codeTips="代码块需要返回一个布尔值(true/false)。例如: true 或 false" />
    </div>

    <!-- 实时预览区域 -->
    <div v-if="isRoot" class="expression-preview">
      <div class="preview-header">
        <div class="header-left">
          <a-icon type="code" theme="filled" />
          <span>表达式预览</span>
        </div>
        <a-tooltip title="复制表达式">
          <a-button type="link" class="copy-btn" @click="copyExpression"> <a-icon type="copy" />复制 </a-button>
        </a-tooltip>
      </div>
      <pre class="preview-content">{{ formattedExpression }}</pre>
    </div>
  </div>
</template>

<script>
import CommonCodeForm from './CommonCodeForm.vue';
export default {
  name: 'LogicExpression',
  components: {
    CommonCodeForm,
  },
  props: {
    value: {
      type: Object,
      default: () => ({
        type: 'AND',
        children: [],
        code: '',
        name: '',
      }),
    },
    isRoot: {
      type: Boolean,
      default: true,
    },
    needLogicType: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    currentValue: {
      get() {
        return this.value;
      },
      set(val) {
        if (this.isRoot && val.type === 'NODE') {
          val.type = 'AND';
        }
        this.$emit('input', val);
      },
    },
    formattedExpression() {
      return this.generateExpression(this.currentValue);
    },
  },
  methods: {
    addChild() {
      if (this.currentValue.type !== 'NODE') {
        this.currentValue.children.push({
          type: 'NODE',
          children: [],
          code: '',
          name: '',
        });
      }
    },
    removeChild(index) {
      this.$confirm({
        title: '提示',
        content: '确定要删除该表达式吗？',
        onOk: () => {
          this.currentValue.children.splice(index, 1);
        },
      });
    },
    generateExpression(node) {
      // 处理空节点
      if (!node) return '';

      // 处理叶子节点
      if (!node.children || node.children.length === 0) {
        return node.name || node.code || '';
      }

      // 递归处理子节点
      const childExpressions = node.children
        .map((child) => {
          // 如果子节点有自己的子节点，递归生成表达式
          if (child.children && child.children.length > 0) {
            return this.generateExpression(child);
          }
          // 如果子节点是逻辑节点但没有子节点，生成简单表达式
          else if (child.type && child.type !== 'NODE') {
            return `${child.type}(${child.name || child.code || ''})`;
          }
          // 普通节点直接返回值
          return child.name || child.code || '';
        })
        .filter((exp) => exp); // 过滤空值

      // 如果没有有效的子表达式，返回当前节点的值
      if (childExpressions.length === 0) {
        return node.name || node.code || '';
      }

      // 生成当前层级的表达式
      return `${node.type}(${childExpressions.join(', ')})`;
    },
    copyExpression() {
      try {
        navigator.clipboard.writeText(this.formattedExpression);
        this.$message.success('复制成功');
      } catch (err) {
        this.$message.error('复制失败，请手动复制');
      }
    },
    validate() {
      const errors = [];

      // 如果有子节点，验证子节点
      if (this.currentValue.children && this.currentValue.children.length > 0) {
        // 验证子节点数量
        if (this.currentValue.type === 'NOT' && this.currentValue.children.length > 1) {
          errors.push('NOT 运算只能有一个子表达式');
        }

        // 验证子节点内容
        const invalidNodes = this.currentValue.children.filter((child) => !child.name && !child.code);

        if (invalidNodes.length > 0) {
          errors.push('请完善所有子表达式的内容');
        }
      } else {
        // 如果是叶子节点，验证名称和代码
        if (!this.currentValue.name && !this.currentValue.code) {
          errors.push('请输入名称或代码');
        }
      }

      const valid = errors.length === 0;
      const error = errors.join('; ');

      return { valid, error };
    },
    // 添加清除错误的方法
    clearErrors() {
      this.$emit('expression-change', this.formattedExpression, true, '');
    },
  },
  watch: {
    'currentValue.type': {
      handler(newType) {
        if (this.isRoot && newType === 'NODE') {
          this.currentValue.type = 'AND';
          this.$message.warning('根节点不能设置为普通节点类型');
        }
      },
    },
    currentValue: {
      deep: true,
      handler(val) {
        // 每次值变化时进行验证
        const { valid, error } = this.validate();
        this.$emit('expression-change', this.formattedExpression, valid, error);
      },
    },
    value: {
      immediate: true,
      handler(newVal) {
        if (this.isRoot && (!newVal.children || newVal.children.length === 0)) {
          this.$nextTick(() => {
            this.currentValue.children = [
              {
                type: 'NODE',
                children: [],
                name: '',
                code: '',
              },
            ];
          });
        }
      },
    },
  },
};
</script>
<style lang="less" scoped>
.logic-expression {
  .form-section {
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 2px;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  :deep(.ant-form-item) {
    margin-bottom: 16px;
    display: flex;
    flex-direction: row;
    align-items: flex-start;

    &:last-child {
      margin-bottom: 0;
    }
    .ant-form-item-control-wrapper {
      flex: 1;
      width: 100%;
    }
  }

  .logic-header {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .logic-children {
    margin: 0 0 24px 100px;
    padding: 16px;
    background: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
  }

  .logic-child {
    margin-bottom: 16px;
    padding: 16px;
    background: #fff;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    &:last-child {
      margin-bottom: 0;
    }

    .logic-child-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px dashed #f0f0f0;

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;

        .child-tag {
          padding: 2px 8px;
          background: #e6f7ff;
          border: 1px solid #91d5ff;
          border-radius: 2px;
          color: #00b099;
          font-size: 12px;
        }

        .child-index {
          color: #00b099;
          font-weight: 500;
        }
      }

      .delete-btn {
        color: #ff4d4f;

        &:hover {
          color: #ff7875;
        }

        .anticon {
          margin-right: 4px;
        }
      }
    }

    .child-name {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }
  }

  .expression-preview {
    margin: 24px 0 0 0;
    padding: 16px;
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 4px;

    .preview-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;

        .anticon {
          color: #00b099;
          font-size: 16px;
        }

        span {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: 500;
        }
      }

      .copy-btn {
        color: #00b099;

        &:hover {
          color: #33bfad;
        }

        .anticon {
          margin-right: 4px;
        }
      }
    }

    .preview-content {
      padding: 16px;
      margin: 0;
      background: #fff;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      white-space: pre-wrap;
      word-break: break-all;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
      font-size: 13px;
      line-height: 1.6;
      color: #333;
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    }
  }
}
</style>
