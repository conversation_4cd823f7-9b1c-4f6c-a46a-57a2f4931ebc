<template>
  <common-code-form
    :formData="formData"
    codeTips="条件代码需要返回布尔值(true/false)。例如: count &lt; 10 或 !list.isEmpty()"
  />
</template>

<script>
import CommonCodeForm from './CommonCodeForm.vue';
export default {
  name: 'SWITCHForm',
  components: {
    CommonCodeForm,
  },
  props: {
    formData: {
      type: Object,
      default: () => ({}),
    },
  },

  methods: {},
  watch: {},
};
</script>

<style lang="less" scoped></style>
