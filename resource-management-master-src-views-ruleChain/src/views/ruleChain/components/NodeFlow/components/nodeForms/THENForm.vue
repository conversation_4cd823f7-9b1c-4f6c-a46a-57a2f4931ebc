<template>
  <div>
    <a-form-model-item label="节点类型" prop="idType" :rules="[{ required: true, message: '请选择节点类型' }]">
      <a-radio-group v-model="formData.idType">
        <a-radio-button value="NORMAL">普通节点</a-radio-button>
        <a-radio-button value="PRE">前置节点</a-radio-button>
        <a-radio-button value="FINALLY">后置节点</a-radio-button>
      </a-radio-group>
    </a-form-model-item>
    <a-form-model-item label="参数类型" prop="paramType" :rules="[{ required: true, message: '请选择参数类型' }]">
      <a-radio-group v-model="formData.paramType">
        <a-radio-button value="normal">无</a-radio-button>
        <a-radio-button value="data">data</a-radio-button>
        <a-radio-button value="bind">bind</a-radio-button>
      </a-radio-group>
    </a-form-model-item>

    <template v-if="!formData.isBuiltIn">
      <!-- data参数类型 - 允许JSON输入 -->
      <a-form-model-item
        v-if="formData.paramType === 'data'"
        label="参数值"
        prop="paramsValue"
        :rules="[{ required: true, message: '请输入参数值' }]"
      >
        <sm-code-editor v-model="formData.paramsValue" mode="json" :validate="true" :show-error="true" />
      </a-form-model-item>

      <!-- bind参数类型 - 输入标签 -->
      <a-form-model-item
        v-if="formData.paramType === 'bind'"
        label="关键词"
        prop="paramsValue"
        :rules="[{ required: true, message: '请输入关键词' }]"
      >
        <a-select v-model="formData.paramsValue" placeholder="请选择标签" style="width: 100%" mode="tags">
          <a-select-option v-for="tag in []" :key="tag" :value="tag">{{ tag }}</a-select-option>
        </a-select>
      </a-form-model-item>
    </template>

    <common-code-form :formData="formData" @update:formData="handleFormDataUpdate" />
  </div>
</template>

<script>
import CommonCodeForm from './CommonCodeForm.vue';
export default {
  name: 'THENForm',
  components: {
    CommonCodeForm,
  },
  props: {
    formData: {
      type: Object,
      default: () => ({
        idType: 'NORMAL',
        paramType: 'normal',
        paramsValue: undefined,
        params: {},
        serviceCode: '',
        dataCode: '',
        objectClass: '',
        dbType: 'mysql',
        customSql: '',
        expression: '',
      }),
    },
  },
  methods: {
    handleFormDataUpdate(newFormData) {
      this.$emit('update:formData', {
        ...this.formData,
        ...newFormData,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.logic-card {
  background: #fafafa;

  :deep(.ant-card-body) {
    padding: 12px;
  }
}
</style>
