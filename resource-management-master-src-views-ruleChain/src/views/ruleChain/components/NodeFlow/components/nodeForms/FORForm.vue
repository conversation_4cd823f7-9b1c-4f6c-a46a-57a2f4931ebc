<template>
  <div>
    <a-form-model-item
      label="异步循环模式"
      :rules="[{ required: true, message: '请选择是否支持异步循环模式' }]"
      prop="parallel"
    >
      <a-tooltip title="开启后循环子项将并行执行">
        <a-checkbox v-model="formData.parallel"></a-checkbox>
      </a-tooltip>
    </a-form-model-item>

    <a-form-model-item label="循环类型" prop="loopType" :rules="[{ required: true, message: '请选择循环类型' }]">
      <a-radio-group v-model="formData.loopType">
        <a-radio-button value="FIXED">固定次数</a-radio-button>
        <a-radio-button value="DYNAMIC">动态次数</a-radio-button>
      </a-radio-group>
    </a-form-model-item>

    <a-form-model-item
      v-if="formData.loopType === 'FIXED'"
      label="固定次数"
      prop="loopCount"
      :rules="[{ required: true, message: '请输入循环次数' }]"
    >
      <a-input-number
        v-model="formData.loopCount"
        :min="1"
        :max="999"
        placeholder="请输入循环次数"
        style="width: 100%"
      />
    </a-form-model-item>

    <common-code-form
      v-if="formData.loopType === 'DYNAMIC'"
      :formData="formData"
      codeTips="代码需要返回一个整数值作为循环次数。例如: list.size() 或 count + 1"
    />
  </div>
</template>

<script>
import CommonCodeForm from './CommonCodeForm.vue';
export default {
  name: 'FORForm',
  components: {
    CommonCodeForm,
  },
  props: {
    formData: {
      type: Object,
      default: () => ({
        loopType: 'FIXED',
        loopCount: 1,
        loopCode: '',
        parallel: false,
      }),
    },
  },
};
</script>

<style lang="less" scoped></style>
