<template>
  <div>
    <a-form-model-item
      label="内置脚本"
      prop="isBuiltIn"
      :rules="formRequired ? [{ required: false, message: '请选择是否使用内置脚本' }] : []"
    >
      <a-switch v-model="formData.isBuiltIn" @change="handleBuiltInChange" />
    </a-form-model-item>

    <a-form-model-item
      label="脚本标识"
      prop="scriptName"
      :rules="formRequired ? [{ required: false, message: '请输入脚本标识' }] : []"
    >
      <template v-if="formData.isBuiltIn">
        <a-select v-model="formData.scriptName" placeholder="请选择脚本标识" @change="handleScriptNameChange">
          <a-select-option v-for="item in dictList" :key="item.value" :value="item.value">{{
            item.label
          }}</a-select-option>
        </a-select>
      </template>
      <template v-else>
        <a-input v-model="formData.scriptName" placeholder="请输入脚本标识" />
      </template>
    </a-form-model-item>

    <div class="form-section">
      <template v-if="!formData.isBuiltIn">
        <a-form-model-item
          label="脚本代码"
          prop="scriptCode"
          :rules="formRequired ? [{ required: false, message: '请输入脚本代码' }] : []"
        >
          <sm-code-editor
            v-model="formData.scriptCode"
            mode="java"
            :api="precompileApi"
            :validate="false"
            :showError="false"
          />
          <div v-if="codeTips" class="tip-text">{{ codeTips }}</div>
        </a-form-model-item>
      </template>
    </div>

    <template v-if="formData.isBuiltIn && formData.scriptName === 'fastDataCmp'">
      <a-form-model-item
        label="Service Code"
        prop="paramsValue.serviceCode"
        :rules="[{ required: true, message: '请输入Service Code' }]"
      >
        <a-input v-model="formData.paramsValue.serviceCode" placeholder="请输入Service Code" />
      </a-form-model-item>

      <a-form-model-item
        label="数据标识"
        prop="paramsValue.dataCode"
        :rules="[{ required: true, message: '请输入数据标识' }]"
      >
        <a-input v-model="formData.paramsValue.dataCode" placeholder="请输入数据标识" />
      </a-form-model-item>

      <a-form-model-item label="查询参数" prop="paramsValue.params">
        <a-row :gutter="4">
          <a-col :span="22">
            <a-table :dataSource="paramsArray" :pagination="false" rowKey="id" size="small">
              <a-table-column title="Key" dataIndex="key">
                <template slot-scope="text, record">
                  <a-input
                    :value="record.key"
                    placeholder="请输入key"
                    @blur="(e) => handleKeyChange(e, record)"
                    :class="{ 'has-error': record.keyError }"
                  />
                  <span class="error-text" v-if="record.keyError">{{ record.keyError }}</span>
                </template>
              </a-table-column>
              <a-table-column title="Value" dataIndex="value">
                <template slot-scope="text, record">
                  <a-input
                    :value="record.value"
                    placeholder="请输入value"
                    @blur="(e) => handleValueChange(e, record)"
                  />
                </template>
              </a-table-column>
              <a-table-column title="操作" width="100px">
                <template slot-scope="text, record">
                  <a-button type="link" @click="removeParamRow(record.id)" icon="delete" />
                </template>
              </a-table-column>
            </a-table>
          </a-col>
          <a-col :span="2">
            <a-button type="link" @click="addParamRow" icon="plus">添加参数</a-button>
          </a-col>
        </a-row>
      </a-form-model-item>

      <a-form-model-item label="序列化对象" prop="paramsValue.objectClass">
        <a-input
          v-model="formData.paramsValue.objectClass"
          placeholder="示例: com.bangdao.data.center.dal.mysql.entity.SaleSettleEntity"
        />
      </a-form-model-item>
    </template>

    <template v-if="formData.isBuiltIn && formData.scriptName === 'dbCmp'">
      <a-form-model-item
        label="数据库类型"
        prop="paramsValue.dbType"
        :rules="[{ required: true, message: '请选择数据库类型' }]"
      >
        <a-select v-model="formData.paramsValue.dbType" placeholder="请选择数据库类型">
          <a-select-option value="mysql">MySQL</a-select-option>
          <a-select-option value="ck">CK</a-select-option>
        </a-select>
      </a-form-model-item>

      <a-form-model-item
        label="自定义SQL"
        prop="paramsValue.customSql"
        :rules="[{ required: true, message: '请输入SQL语句' }]"
      >
        <a-textarea v-model="formData.paramsValue.customSql" :rows="4" placeholder="请输入SQL语句" />
      </a-form-model-item>

      <a-form-model-item
        label="数据标识"
        prop="paramsValue.dataCode"
        :rules="[{ required: true, message: '请输入数据标识' }]"
      >
        <a-input v-model="formData.paramsValue.dataCode" placeholder="请输入数据标识" />
      </a-form-model-item>

      <a-form-model-item label="序列化对象" prop="paramsValue.objectClass">
        <a-input
          v-model="formData.paramsValue.objectClass"
          placeholder="示例: com.bangdao.data.center.dal.mysql.entity.SaleSettleEntity"
        />
      </a-form-model-item>
    </template>
    <template v-if="formData.isBuiltIn && formData.scriptName === 'iteratorCmp'">
      <a-form-model-item
        label="数据标识"
        prop="paramsValue.dataCode"
        :rules="[{ required: true, message: '请输入数据标识' }]"
      >
        <a-input v-model="formData.paramsValue.dataCode" placeholder="请输入数据标识" />
      </a-form-model-item>
    </template>

    <!-- booleanCmp -->
    <template v-if="formData.isBuiltIn && formData.scriptName === 'booleanCmp'">
      <a-form-model-item
        label="表达式"
        prop="paramsValue.expression"
        :rules="[{ required: true, message: '请输入表达式' }]"
      >
        <a-input v-model="formData.paramsValue.expression" placeholder="请输入表达式" />
      </a-form-model-item>
    </template>

    <!-- calculateCmp -->
    <template v-if="formData.isBuiltIn && formData.scriptName === 'calculateCmp'">
      <a-form-model-item
        label="结果集"
        prop="paramsValue.target"
        :rules="[{ required: true, message: '请输入结果集' }]"
      >
        <a-input v-model="formData.paramsValue.target" placeholder="请输入结果集" />
      </a-form-model-item>

      <a-form-model-item
        label="计算公式"
        prop="paramsValue.formulaList"
        :rules="[{ required: true, message: '请添加计算公式', validator: validateTable }]"
      >
        <a-table :dataSource="formData.paramsValue.formulaList" :pagination="false" size="small" rowKey="orderNo">
          <a-table-column title="序号" dataIndex="orderNo" width="80px">
            <template slot-scope="text">{{ text + 1 }}</template>
          </a-table-column>

          <a-table-column title="公式" dataIndex="formula">
            <template slot-scope="text, record">
              <a-input v-model="record.formula" placeholder="请输入公式" @change="() => validateFormula(record)" />
            </template>
          </a-table-column>

          <a-table-column title="舍入模式" dataIndex="roundingMode">
            <template slot-scope="text, record">
              <a-select
                v-model="record.roundingMode"
                placeholder="请选择舍入模式"
                style="width: 100%"
                @change="() => validateFormula(record)"
              >
                <a-select-option v-for="item in roundingModes" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </template>
          </a-table-column>

          <a-table-column title="截断位数" dataIndex="roundingDigits">
            <template slot-scope="text, record">
              <a-input-number
                v-model="record.roundingDigits"
                :min="0"
                style="width: 100%"
                @change="() => validateFormula(record)"
              />
            </template>
          </a-table-column>

          <a-table-column title="结果键名" dataIndex="dataKey">
            <template slot-scope="text, record">
              <a-input v-model="record.dataKey" placeholder="请输入结果键名" @change="() => validateFormula(record)" />
            </template>
          </a-table-column>

          <a-table-column title="操作" width="150px">
            <template slot-scope="text, record, index">
              <a-space>
                <a-button type="link" icon="arrow-up" :disabled="index === 0" @click="moveFormula(index, 'up')">
                  上移
                </a-button>
                <a-button
                  type="link"
                  icon="arrow-down"
                  :disabled="index === formData.paramsValue.formulaList.length - 1"
                  @click="moveFormula(index, 'down')"
                >
                  下移
                </a-button>
                <a-button type="link" icon="delete" @click="removeFormula(record.orderNo)"> 删除 </a-button>
              </a-space>
            </template>
          </a-table-column>
        </a-table>
        <div style="margin-top: 8px">
          <a-button type="link" icon="plus" @click="addFormula">添加公式</a-button>
        </div>
      </a-form-model-item>
    </template>
  </div>
</template>

<script>
import { getDict } from '@/api/system/dict';
export default {
  name: 'CommonCodeForm',
  data() {
    return {
      precompileApi: this.api.data_item_precompile,
      paramsArray: [],
      dictList: [],
      roundingModes: [
        { label: '四舍五入', value: 'HALF_UP' },
        { label: '进位', value: 'UP' },
        { label: '截断', value: 'DOWN' },
      ],
    };
  },
  computed: {
    // 计算是否有参数错误
    hasParamsError() {
      return this.paramsArray.some((item) => item.keyError || !item.key || !item.value);
    },
    // 计算有效的参数对象
    validParams() {
      const params = {};
      this.paramsArray.forEach((item) => {
        if (item.key && item.value) {
          params[item.key] = item.value;
        }
      });
      return Object.keys(params).length > 0 ? params : undefined;
    },
  },
  props: {
    formData: {
      type: Object,
      default: () => ({
        scriptCode: '',
        scriptName: '',
        isBuiltIn: false,
        paramsValue: {
          serviceCode: '',
          dataCode: '',
          params: {},
          objectClass: '',
          dbType: 'mysql',
          customSql: '',
          expression: '',
        },
      }),
    },
    codeTips: {
      type: String,
      default: '',
    },
    formRequired: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    handleBuiltInChange() {
      this.$set(this.formData, 'scriptCode', undefined);
      this.$set(this.formData, 'scriptName', undefined);
    },
    handleScriptNameChange(value) {
      // 定义不同脚本类型的默认参数值
      const defaultParams = {
        fastDataCmp: {
          serviceCode: '',
          dataCode: '',
          params: {},
          objectClass: '',
        },
        dbCmp: {
          dbType: 'mysql',
          customSql: '',
          dataCode: '',
          objectClass: '',
        },
        iteratorCmp: {
          dataCode: '',
        },
        booleanCmp: {
          expression: '',
          dataCode: '',
        },
        calculateCmp: {
          target: '',
          formulaList: [],
        },
      };

      // 根据脚本类型重置表单数据
      if (defaultParams[value]) {
        const newFormData = {
          ...this.formData,
          paramsValue: defaultParams[value],
        };
        this.$emit('update:formData', newFormData);
        this.paramsArray = [];
      }
    },

    // 初始化参数数组
    initParamsArray(params = {}) {
      // 使用深拷贝确保数据独立性
      const paramsCopy = JSON.parse(JSON.stringify(params));
      this.paramsArray = Object.entries(paramsCopy).map(([key, value]) => ({
        id: Date.now(),
        key,
        value,
        keyError: '',
      }));
    },

    // 添加新参数行
    addParamRow() {
      this.paramsArray.push({
        id: Date.now(),
        key: '',
        value: '',
        keyError: '',
      });
    },

    // 删除参数行
    removeParamRow(id) {
      const index = this.paramsArray.findIndex((item) => item.id === id);
      if (index > -1) {
        this.paramsArray.splice(index, 1);
      }
    },

    // 验证并更新参数
    validateAndUpdateParams() {
      if (!this.hasParamsError) {
        const paramsCopy = JSON.parse(JSON.stringify(this.validParams || {}));
        const newFormData = {
          ...this.formData,
          paramsValue: {
            ...this.formData.paramsValue,
            params: paramsCopy,
          },
        };
        this.$nextTick(() => {
          this.$emit('update:formData', newFormData);
        });
      }
    },

    // 处理key值变更
    handleKeyChange(e, record) {
      if (!record) return;
      const value = e.target.value;
      this.$set(record, 'key', value);
      const isDuplicate = this.paramsArray.some((item) => item.id !== record.id && item.key === record.key);
      if (isDuplicate) {
        record.keyError = 'key值不能重复';
      } else {
        record.keyError = '';
        // 立即更新params
        this.validateAndUpdateParams();
      }
    },

    // 处理value值变更
    handleValueChange(e, record) {
      if (!record) return;
      const value = e.target.value;
      this.$set(record, 'value', value);
      // 立即更新params
      this.validateAndUpdateParams();
    },

    // 获取字典列表
    async getDictList() {
      const [res] = await getDict('liteflow_chain_component');
      this.dictList = (res.data || []).map((item) => ({
        label: item.dictLabel,
        value: item.dictKey,
      }));
    },

    // 添加新公式
    addFormula() {
      if (!this.formData.paramsValue.formulaList) {
        this.$set(this.formData.paramsValue, 'formulaList', []);
      }

      const newFormula = {
        orderNo: this.formData.paramsValue.formulaList.length,
        formula: '',
        roundingMode: 'HALF_UP',
        roundingDigits: 0,
        dataKey: '',
      };

      this.formData.paramsValue.formulaList.push(newFormula);
    },

    // 删除公式
    removeFormula(orderNo) {
      const index = this.formData.paramsValue.formulaList.findIndex((item) => item.orderNo === orderNo);
      if (index > -1) {
        this.formData.paramsValue.formulaList.splice(index, 1);
        // 重新计算序号
        this.formData.paramsValue.formulaList.forEach((item, idx) => {
          item.orderNo = idx;
        });
      }
    },

    // 移动公式
    moveFormula(index, direction) {
      const list = this.formData.paramsValue.formulaList;
      if (direction === 'up' && index > 0) {
        const temp = list[index];
        this.$set(list, index, list[index - 1]);
        this.$set(list, index - 1, temp);
        // 更新序号
        list.forEach((item, idx) => {
          item.orderNo = idx;
        });
      } else if (direction === 'down' && index < list.length - 1) {
        const temp = list[index];
        this.$set(list, index, list[index + 1]);
        this.$set(list, index + 1, temp);
        // 更新序号
        list.forEach((item, idx) => {
          item.orderNo = idx;
        });
      }
    },

    // 验证公式
    validateFormula(record) {
      // 这里可以添加具体的验证逻辑
      if (!record.formula || !record.dataKey) {
        return false;
      }
      return true;
    },
    validateTable(rule, value, callback) {
      // 检查表格数据是否为空
      if (!value || value.length === 0) {
        callback(new Error('请添加计算公式'));
        return;
      }

      // 检查每一行数据是否完整
      for (let i = 0; i < value.length; i++) {
        const row = value[i];
        // 检查公式
        if (!row.formula || !row.formula.trim()) {
          callback(new Error(`第${i + 1}行公式不能为空`));
          return;
        }
        // 检查舍入模式
        if (!row.roundingMode) {
          callback(new Error(`第${i + 1}行舍入模式不能为空`));
          return;
        }
        // 检查截断位数
        if (typeof row.roundingDigits !== 'number' || row.roundingDigits < 0) {
          callback(new Error(`第${i + 1}行截断位数必须为非负数`));
          return;
        }
        // 检查结果键名
        if (!row.dataKey || !row.dataKey.trim()) {
          callback(new Error(`第${i + 1}行结果键名不能为空`));
          return;
        }
      }

      // 检查结果键名是否重复
      const dataKeys = value.map((item) => item.dataKey.trim());
      const uniqueDataKeys = new Set(dataKeys);
      if (dataKeys.length !== uniqueDataKeys.size) {
        callback(new Error('结果键名不能重复'));
        return;
      }

      callback();
    },
  },
  watch: {
    'formData.isBuiltIn': {
      immediate: true,
      handler(newVal) {
        if (newVal && this.formData.scriptCode) {
          const newFormData = {
            ...this.formData,
            scriptCode: '',
          };
          this.$emit('update:formData', newFormData);
        }
      },
    },
    'formData.paramsValue.params': {
      immediate: true,
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          // 使用深拷贝初始化参数数组
          this.initParamsArray(newVal);
        } else {
          this.paramsArray = [];
        }
      },
    },
  },
  mounted() {
    this.getDictList();
    // 确保初始化时设置正确的默认值
    if (this.formData.isBuiltIn && !this.formData.scriptName) {
      this.$set(this.formData, 'scriptName', '');
      this.$set(this.formData, 'paramsValue', {
        serviceCode: '',
        dataCode: '',
        params: {},
        objectClass: '',
        dbType: 'mysql',
        customSql: '',
        expression: '',
        target: '',
        formulaList: [],
      });
    }
  },
};
</script>
<style lang="less" scoped>
.tip-text {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.5;
}

.has-error {
  border-color: #ff4d4f !important;
}

.error-text {
  color: #ff4d4f;
  font-size: 12px;
  display: block;
  margin-top: 4px;
}

.params-table {
  .ant-table-tbody > tr > td {
    padding: 8px;
  }
}
</style>
