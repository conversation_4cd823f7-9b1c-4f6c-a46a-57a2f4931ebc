/**
 * 获取节点的默认表单数据
 * @param {string} nodeType - 节点类型
 * @returns {Object} 默认的表单数据
 */
export function getDefaultFormData(nodeType) {
  if (!nodeType) {
    throw new Error('节点类型不能为空');
  }

  if (typeof nodeType !== 'string') {
    throw new Error('节点类型必须是字符串');
  }

  const commonCodeParams = {
    scriptName: '',
    scriptCode: '',
    isBuiltIn: true,
    serviceCode: '',
    dataCode: '',
    paramsValue: {
      serviceCode: '',
      dataCode: '',
      params: {},
      objectClass: '',
      dbType: 'mysql',
      customSql: '',
      expression: '',
    },
  };

  switch (nodeType) {
    case 'THEN':
      return {
        idType: 'NORMAL',
        paramType: 'normal',
        ...commonCodeParams,
      };
    case 'IF':
      return {
        condition: '',
        isLogicNode: false,
        logicExpression: {
          type: 'AND',
          children: [],
          name: '',
          code: '',
        },
        ...commonCodeParams,
      };
    case 'FOR':
      return {
        loopType: 'FIXED',
        loopCount: 1,
        loopCode: '',
        parallel: false,
        ...commonCodeParams,
      };
    case 'WHILE':
      return {
        condition: '',
        parallel: false,
        ...commonCodeParams,
      };
    case 'ITERATOR':
      return {
        iteratorCode: '',
        parallel: false,
        ...commonCodeParams,
      };
    case 'SWITCH':
      return {
        conditions: [{ value: 'DEFAULT', code: '', scriptName: '', scriptCode: '', isBuiltIn: false }],
        ...commonCodeParams,
      };
    case 'TIMEOUT':
      return {
        timeout: 3,
        timeoutHandler: '',
      };
    case 'PAR':
      return {
        parType: 'START',
      };
    default:
      return {};
  }
}
