<template>
  <div>
    <PageWrapper
      ref="myRef"
      title="规则链管理"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      @loadData="loadData"
    >
      <template #defaultHeader>
        <a-button type="primary" @click="showCreateModal">新增规则链</a-button>
      </template>
      <template #chainName="{ row }">
        <a href="javascript:void(0);" @click="goToChainDetail(row)">
          {{ row.chainName }}
        </a>
      </template>
      <template #enable>
        <a-select v-model="filterOptions.params.enable" placeholder="请选择启用状态">
          <a-select-option value="true">已启用</a-select-option>
          <a-select-option value="false">未启用</a-select-option>
        </a-select>
      </template>

      <template #enableCol="{ row }">
        <a-switch v-model="row.enable" :disabled="true" checked-children="已启用" un-checked-children="未启用" />
      </template>

      <template #operate="{ row }">
        <span class="operate-button" @click="showEditModal(row)">编辑</span>
        <span class="operate-button" v-if="!row.enable" @click="handleChainStatus(row, true)">启用</span>
        <span class="operate-button" v-else @click="handleChainStatus(row, false)">禁用</span>
        <span class="operate-button" @click="handleCopy(row)">复制</span>
        <span class="operate-button color-error" @click="onClickDelete(row)">删除</span>
      </template>
    </PageWrapper>

    <rule-chain-modal :modalVisible="modalVisible" :formData="formData" @cancel="handleCancel" />
  </div>
</template>

<script>
import RuleChainModal from './components/RuleChainModal.vue';

export default {
  components: {
    RuleChainModal,
  },
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableProps: {
        showOverflow: 'tooltip',
        columnConfig: { resizable: true },
      },
      tableData: [],
      detail: {},
      tableColumn: [
        // 序号
        {
          title: '序号',
          type: 'seq',
          align: 'center',
          width: 60,
        },
        {
          title: '规则名称',
          field: 'chainName',
          align: 'center',
          minWidth: '120px',
          slots: { default: 'chainName' },
        },
        {
          title: '规则描述',
          field: 'chainDesc',
          align: 'center',
          maxWidth: '200px',
        },
        // 规则链版本
        {
          title: '规则链版本',
          field: 'version',
          align: 'center',
        },
        {
          title: '更新时间',
          field: 'updateTime',
          align: 'center',
        },
        {
          title: '创建人',
          field: 'createUser',
          align: 'center',
        },
        {
          title: '状态',
          field: 'enable',
          slots: { default: 'enableCol' },
          align: 'center',
          width: '80px',
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      filterOptions: {
        config: [
          {
            field: 'chainName',
            title: '规则名称',
            defaultValue: undefined,
          },
          {
            field: 'enable',
            title: '状态',
            element: 'slot',
            slotName: 'enable',
            defaultValue: undefined,
          },
        ],
        params: {
          chainName: undefined,
          enable: undefined,
        },
        gridCol: { xs: 8, sm: 8, md: 8, lg: 8, xl: 8, xxl: 8 },
      },
      modalVisible: false,
      formData: {
        chainName: '',
        chainDesc: '',
      },
      rules: {
        chainName: [
          { required: true, message: '请输入规则链名称', trigger: 'blur' },
          { max: 32, message: '规则链名称最多32个字符', trigger: 'blur' },
        ],
        chainDesc: [{ max: 64, message: '规则链描述最多64个字符', trigger: 'blur' }],
      },
    };
  },
  activated() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      // 实际接口调用
      const [res, err] = await this.$post(this.api.rule_chain_list, {
        ...this.filterOptions.params,
        pageCount: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      });
      this.loading = false;
      if (err) return;
      this.tablePage.total = res?.total || 0;
      this.tableData = res?.data || [];
    },
    showCreateModal() {
      localStorage.setItem('isDebug', 'false');
      this.$router.push({
        name: 'VPP_RESOURCE-ruleChain-details',
      });
    },
    showEditModal(row) {
      this.formData = {
        chainId: row.chainId,
        chainName: row.chainName,
        chainDesc: row.chainDesc,
      };
      this.modalVisible = true;

      console.log('this.formData', this.modalVisible);
    },
    async handleSubmit(formData) {
      const [res, err] = await this.$post(this.api.rule_chain_update, {
        chainId: this.formData.chainId,
        ...formData,
      });

      if (err) {
        return;
      }
      this.$message.success('更新成功');
      this.modalVisible = false;
      this.loadData();
    },
    handleCancel(isSubmit) {
      if (isSubmit) {
        this.handleSubmit(this.formData);
      }
      this.modalVisible = false;
      this.formData = {
        chainName: '',
        chainDesc: '',
      };
    },
    async goToChainDetail(row) {
      localStorage.setItem('isDebug', 'false');
      this.$router.push({
        name: 'VPP_RESOURCE-ruleChain-details',
        query: {
          chainId: row.chainId,
        },
      });
    },
    async handleChainStatus(row, enable) {
      const action = enable ? '启用' : '禁用';
      await this.$confirm({
        title: '提示',
        content: `确定要${action}规则链"${row.chainName}"吗？`,
        onOk: async () => {
          const [res, err] = await this.$get(this.api.rule_chain_enable, {
            id: row.chainId,
            enable,
          });
          if (err) {
            return;
          }
          if (res) {
            this.$message.success(`${action}成功`);
            this.loadData();
          }
        },
      });
    },
    async onClickDelete(row) {
      if (row.enable) {
        this.$message.warning('启用状态的规则链不能删除，请先禁用');
        return;
      }
      await this.$confirm({
        title: '提示',
        content: `确定要删除规则链"${row.chainName}"吗？`,
        onOk: async () => {
          const [res, err] = await this.$get(this.api.rule_chain_delete, {
            id: row.chainId,
          });
          if (err) {
            return;
          }
          if (res) {
            this.$message.success('删除成功');
            this.loadData();
          }
        },
      });
    },
    // 复制规则链
    handleCopy(row) {
      // 先确认是否要复制
      this.$confirm({
        title: '复制规则链',
        content: `确定要复制规则链"${row.chainName}"吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          const [res, err] = await this.$get(this.api.rule_chain_detail, {
            id: row.chainId,
          });
          if (err) {
            this.$message.error('获取规则链详情失败');
            return;
          }
          if (res) {
            console.log('res.data', res.data);
            const copyData = {
              ...res.data,
              chainId: '',
              chainName: `${res.data.chainName}-副本`,
              enable: false,
            };
            const [addRes, addErr] = await this.$post(this.api.rule_chain_add, copyData);
            if (addErr) {
              this.$message.error('复制失败');
              return;
            }
            if (addRes && addRes.data.chainId) {
              this.$message.success('复制成功');
              this.loadData();

              // 弹框提示是否跳转详情
              this.$confirm({
                title: '复制成功',
                content: '是否跳转到详情页进行编辑?',
                okText: '确定',
                cancelText: '取消',
                onOk: () => {
                  localStorage.setItem('isDebug', 'false');
                  this.$router.push({
                    name: 'VPP_RESOURCE-ruleChain-details',
                    query: {
                      chainId: addRes.data.chainId,
                    },
                  });
                },
              });
            } else {
              this.$message.error('复制失败');
            }
          }
        },
      });
    },
  },
};
</script>
