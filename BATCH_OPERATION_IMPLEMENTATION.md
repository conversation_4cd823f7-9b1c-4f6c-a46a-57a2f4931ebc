# 批量操作功能实现说明

## 已完成的修改

### 1. 按钮布局调整

- ✅ 将原有的【批量完结】和【批量转派】按钮整合到【批量操作】el-dropdown 下拉菜单中
- ✅ 在下拉菜单中新增【批量处理】按钮选项
- ✅ 添加【当前页/所有页】选择器，默认选中"当前页"
- ✅ 保持原有的点击逻辑和按钮权限判断

### 2. 数据属性添加

- ✅ 添加 `selectPage: "1"` - 当前页/全部页选择
- ✅ 添加 `batchProcessDialogVisible: false` - 批量处理弹窗控制
- ✅ 添加 `batchProcessCount: 0` - 可处理的工单数量

### 3. 交互逻辑实现

- ✅ 添加 `handleBatchCommand(command)` 方法处理下拉菜单命令
- ✅ 添加页面选择器监听器，切换到"所有页"时清空已勾选项
- ✅ 修改 `checkboxConfig.checkMethod` 在全部页模式时禁用勾选功能
- ✅ 添加 `clearSelectedData()` 方法清空选中数据

### 4. 批量处理功能

- ✅ 实现 `handleBatchProcess()` 方法
- ✅ 实现 `confirmBatchProcess()` 方法
- ✅ 实现 `cancelBatchProcess()` 方法
- ✅ 添加批量处理确认弹窗
- ✅ 在 API 文件中添加 `batchProcess` 接口定义

### 5. 批量完成和批量转派功能增强

- ✅ 修改 `handleBatchEnd()` 和 `handleBatchTransfer()` 方法支持全部页模式
- ✅ 修改 `modalConfirmHandler()` 方法处理全部页模式的参数传递
- ✅ 在全部页模式下传递查询条件给后端

### 6. 用户体验优化

- ✅ 添加 `isBatchOperationDisabled` 计算属性控制按钮禁用状态
- ✅ 当前页模式下未选中数据时禁用批量操作按钮
- ✅ 全部页模式下禁用表格勾选功能

## 功能特性

### 批量操作触发条件

- 当前页模式：必须勾选至少一条数据
- 全部页模式：无需勾选，直接操作所有符合条件的数据

### 数据范围切换逻辑

- 切换到"所有页"时自动清空已勾选项并禁用勾选功能
- 切换到"当前页"时恢复勾选功能

### 数据校验逻辑

- 仅对符合条件的数据执行操作（处理中状态且非待审核）
- 权限校验：系统管理员或当前节点处理人
- 不符合条件的数据自动过滤

### 批量处理弹窗

- 显示实际可处理的工单数量
- 提示文案：`对 xx 条工单的当前未处理节点确定做已处理操作吗？`
- 确认后关闭弹窗并刷新列表

## 待完善的功能

### 1. 后端接口

- [ ] 批量处理接口 `/ledger/order/batchProcess` 需要后端实现
- [ ] 查询可处理总条数的接口需要后端提供
- [ ] 批量完成和批量转派接口需要支持全部页模式参数

### 2. 全部页模式数据统计

- [ ] 需要调用接口获取可处理/可完成/可转派的实际条数
- [ ] 目前使用占位符 0，需要替换为真实数据

## 使用说明

1. 选择数据范围：点击【当前页/所有页】选择器
2. 选择批量操作：点击【批量操作】下拉按钮
3. 选择具体操作：【批量处理】/【批量完成】/【批量转派】
4. 确认操作：在弹窗中确认执行

## 技术实现

- 严格遵循 Vue 2 + Element UI 技术栈
- 保持与现有代码风格一致
- 完整的权限控制逻辑
- 良好的用户体验和交互反馈
